{"user-*************": {"transactions": [{"_id": "default_1_user-*************", "type": "admin_adjustment", "amount": 1000, "description": "Welcome bonus", "status": "completed", "reference": "***************", "createdAt": "2025-06-06T00:41:46.834Z"}, {"_id": "default_2_user-*************", "type": "deposit", "amount": 1500, "description": "Initial deposit", "status": "completed", "reference": "DEP1749256906834", "createdAt": "2025-06-05T00:41:46.834Z"}], "notifications": [{"_id": "welcome_user-*************", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-07T00:41:46.834Z"}], "chats": [], "balance": 2500, "otpData": null}, "user-*************": {"transactions": [{"_id": "txn_admin_1749520704919", "type": "transfer_in", "amount": 20000, "description": "Transfer from mensach derish", "sentFrom": "mensach derish", "status": "completed", "reference": "TIN1749520704919", "createdAt": "2025-06-10T01:58:24.919Z"}, {"_id": "txn_admin_1749321670136", "type": "transfer_in", "amount": 3000, "description": "Transfer from john smith", "sentFrom": "john smith", "status": "completed", "reference": "TIN1749321670136", "createdAt": "2025-06-07T18:41:10.136Z"}, {"_id": "txn_1749315159195", "type": "transfer_out", "amount": 25000, "description": "Transfer to sammy cruise - useful ", "recipientName": "sammy cruise", "accountNumber": "**********", "purpose": "useful ", "status": "completed", "reference": "TRF1749315159195", "createdAt": "2025-06-07T16:52:39.195Z"}, {"_id": "txn_admin_1749307841981", "type": "transfer_in", "amount": 50000, "description": "Transfer from sammy crucru", "sentFrom": "sammy crucru", "status": "completed", "reference": "TIN1749307841981", "createdAt": "2025-06-07T14:50:41.981Z"}, {"_id": "txn_1749304916149", "type": "withdrawal", "amount": 2000, "description": "maintenance ", "status": "completed", "reference": "***************", "createdAt": "2025-06-07T14:01:56.149Z"}, {"_id": "txn_admin_1749261708120", "type": "transfer_in", "amount": 1000, "description": "Transfer from mikel johnson", "sentFrom": "mikel johnson", "status": "completed", "reference": "TIN1749261708120", "createdAt": "2025-06-07T02:01:48.120Z"}, {"_id": "txn_admin_1749258419386", "type": "admin_adjustment", "amount": 5000, "description": "no reason", "status": "completed", "reference": "ADJ1749258419386", "createdAt": "2025-06-07T01:06:59.386Z"}, {"_id": "txn_1749257162294", "type": "withdrawal", "amount": 500, "description": "maintenance ", "status": "completed", "reference": "***************", "createdAt": "2025-06-07T00:46:02.294Z"}, {"_id": "default_1_user-*************", "type": "admin_adjustment", "amount": 1000, "description": "Welcome bonus", "status": "completed", "reference": "WB*************", "createdAt": "2025-06-06T00:44:09.948Z"}, {"_id": "default_2_user-*************", "type": "deposit", "amount": 1500, "description": "Initial deposit", "status": "completed", "reference": "DEP*************", "createdAt": "2025-06-05T00:44:09.948Z"}], "notifications": [{"_id": "notif_admin_1749520704919", "message": "Transfer received: $20000.00 from mensach derish. Your account balance is now $54000.00.", "type": "success", "read": false, "createdAt": "2025-06-10T01:58:24.919Z"}, {"_id": "notif_admin_1749321670136", "message": "Transfer received: $3000.00 from john smith. Your account balance is now $34000.00.", "type": "success", "read": false, "createdAt": "2025-06-07T18:41:10.136Z"}, {"_id": "notif_1749316009596", "message": "Your transfer OTP is: 268666. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T17:06:49.596Z"}, {"_id": "notif_1749316009589", "message": "Your transfer OTP is: 199347. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T17:06:49.589Z"}, {"_id": "notif_1749315888771", "message": "Your transfer OTP is: 287858. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T17:04:48.771Z"}, {"_id": "notif_1749315888763", "message": "Your transfer OTP is: 901751. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T17:04:48.763Z"}, {"_id": "notif_1749315807628", "message": "Your transfer OTP is: 703159. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T17:03:27.628Z"}, {"_id": "notif_1749315807622", "message": "Your transfer OTP is: 750027. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T17:03:27.622Z"}, {"_id": "notif_1749315564462", "message": "Your transfer OTP is: 721519. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T16:59:24.462Z"}, {"_id": "notif_1749315564457", "message": "Your transfer OTP is: 784478. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T16:59:24.457Z"}, {"_id": "notif_1749315159196_success", "message": "Transfer of $25000 to sammy cruise has been processed successfully. Transaction ID: TRF1749315159195", "type": "success", "read": false, "createdAt": "2025-06-07T16:52:39.196Z"}, {"_id": "notif_1749315104919", "message": "Your transfer OTP is: 821460. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T16:51:44.919Z"}, {"_id": "notif_1749315104914", "message": "Your transfer OTP is: 427929. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T16:51:44.914Z"}, {"_id": "notif_admin_1749307841981", "message": "Transfer received: $50000.00 from sammy crucru. Your account balance is now $56000.00.", "type": "success", "read": false, "createdAt": "2025-06-07T14:50:41.981Z"}, {"_id": "notif_1749304916150_success", "message": "Withdrawal of $2000 has been processed successfully. Transaction ID: ***************", "type": "success", "read": false, "createdAt": "2025-06-07T14:01:56.150Z"}, {"_id": "notif_1749304906617", "message": "Your withdrawal OTP is: 550076. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T14:01:46.617Z"}, {"_id": "notif_1749304863825", "message": "Your withdrawal OTP is: 139770. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T14:01:03.825Z"}, {"_id": "notif_admin_1749261708120", "message": "Transfer received: $1000.00 from mikel johnson. Your account balance is now $8000.00.", "type": "success", "read": false, "createdAt": "2025-06-07T02:01:48.120Z"}, {"_id": "notif_admin_1749258419386", "message": "Your account balance has been increased by 5000. New balance: $7000.00. Reason: no reason", "type": "success", "read": false, "createdAt": "2025-06-07T01:06:59.386Z"}, {"_id": "notif_1749257162295_success", "message": "Withdrawal of $500 has been processed successfully. Transaction ID: ***************", "type": "success", "read": false, "createdAt": "2025-06-07T00:46:02.295Z"}, {"_id": "notif_1749257152462", "message": "Your withdrawal OTP is: 778548. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T00:45:52.462Z"}, {"_id": "notif_1749257111579", "message": "Your withdrawal OTP is: 965071. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T00:45:11.579Z"}, {"_id": "welcome_user-*************", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-07T00:44:09.948Z"}], "chats": [{"_id": "chat_1749257207225", "user": {"id": "user-*************", "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "accountNumber": "**********"}, "subject": "withdrawal ", "priority": "urgent", "status": "open", "messages": [{"_id": "msg_1749257207225", "sender": {"id": "user-*************", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "not getting my money", "type": "text", "createdAt": "2025-06-07T00:46:47.225Z"}, {"_id": "msg_1749257209234_agent", "sender": {"id": "agent-1", "firstName": "Support", "lastName": "Agent", "role": "agent"}, "content": "Hello! Thank you for contacting SecureBank support. An agent will join you shortly to assist with your inquiry. Please feel free to provide any additional details while you wait.", "type": "text", "createdAt": "2025-06-07T00:46:49.234Z"}, {"_id": "msg_admin_1749258374581", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "Thank you for your patience. What can i do for you?", "type": "text", "createdAt": "2025-06-07T01:06:14.581Z"}, {"_id": "msg_1749258489337", "sender": {"id": "user-*************", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "i need to get my money", "type": "text", "createdAt": "2025-06-07T01:08:09.337Z"}, {"_id": "msg_1749261815770", "sender": {"id": "user-*************", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "Thank you", "type": "text", "createdAt": "2025-06-07T02:03:35.770Z"}, {"_id": "msg_admin_1749262817618", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "okay", "type": "text", "createdAt": "2025-06-07T02:20:17.618Z"}, {"_id": "msg_admin_1749262840123", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "come next time", "type": "text", "createdAt": "2025-06-07T02:20:40.123Z"}, {"_id": "msg_admin_1749262848573", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "see you", "type": "text", "createdAt": "2025-06-07T02:20:48.573Z"}, {"_id": "msg_1749262906662", "sender": {"id": "user-*************", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "alright ", "type": "text", "createdAt": "2025-06-07T02:21:46.662Z"}, {"_id": "msg_1749304965073", "sender": {"id": "user-*************", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "hey", "type": "text", "createdAt": "2025-06-07T14:02:45.073Z"}, {"_id": "msg_1749321400943", "sender": {"id": "user-*************", "firstName": "perpetual", "lastName": "odosa", "role": "user"}, "content": "i need the otp code", "type": "text", "createdAt": "2025-06-07T18:36:40.943Z"}, {"_id": "msg_admin_1749321610109", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "alright hold", "type": "text", "createdAt": "2025-06-07T18:40:10.109Z"}, {"_id": "msg_1749327396011", "sender": {"id": "user-*************", "firstName": "perpetual", "lastName": "odosa", "role": "user"}, "content": "hey", "type": "text", "createdAt": "2025-06-07T20:16:36.011Z"}, {"_id": "msg_1749502830157", "sender": {"id": "user-*************", "firstName": "perpetual", "lastName": "od", "role": "user"}, "content": "hello", "type": "text", "createdAt": "2025-06-09T21:00:30.157Z"}, {"_id": "msg_admin_1749502916566", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "hey", "type": "text", "createdAt": "2025-06-09T21:01:56.566Z"}, {"_id": "msg_1749512719673", "sender": {"id": "user-*************", "firstName": "perpetual", "lastName": "od", "role": "user"}, "content": "hi", "type": "text", "createdAt": "2025-06-09T23:45:19.673Z"}, {"_id": "msg_1749513030973", "sender": {"id": "user-*************", "firstName": "perpetual", "lastName": "od", "role": "user"}, "content": "hey", "type": "text", "createdAt": "2025-06-09T23:50:30.973Z"}, {"_id": "msg_admin_1749513110401", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "hello", "type": "text", "createdAt": "2025-06-09T23:51:50.401Z"}, {"_id": "msg_admin_1749513134308", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "how may i help u", "type": "text", "createdAt": "2025-06-09T23:52:14.308Z"}, {"_id": "msg_admin_1749514797955", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "yo", "type": "text", "createdAt": "2025-06-10T00:19:57.955Z"}, {"_id": "msg_1749514837663", "sender": {"id": "user-*************", "firstName": "perpetual", "lastName": "od", "role": "user"}, "content": "hey", "type": "text", "createdAt": "2025-06-10T00:20:37.663Z"}, {"_id": "msg_1749521810805", "sender": {"id": "user-*************", "firstName": "perpetual", "lastName": "od", "role": "user"}, "content": "hello", "type": "text", "createdAt": "2025-06-10T02:16:50.805Z"}, {"_id": "msg_admin_1749521877462", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "How're you?", "type": "text", "createdAt": "2025-06-10T02:17:57.462Z"}], "createdAt": "2025-06-07T00:46:47.225Z", "lastActivity": "2025-06-10T02:17:57.462Z"}], "balance": 54000, "otpData": {"otp": "737255", "amount": 34000, "recipientName": "sammy cruise", "accountNumber": "**********", "purpose": "useful ", "timestamp": *************, "used": false}}, "user-1": {"transactions": [{"_id": "default_1_user-1", "type": "admin_adjustment", "amount": 1000, "description": "Welcome bonus", "status": "completed", "reference": "***************", "createdAt": "2025-06-06T00:54:50.547Z"}, {"_id": "default_2_user-1", "type": "deposit", "amount": 1500, "description": "Initial deposit", "status": "completed", "reference": "DEP1749257690547", "createdAt": "2025-06-05T00:54:50.547Z"}], "notifications": [{"_id": "welcome_user-1", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-07T00:54:50.547Z"}], "chats": [{"_id": "chat_1749264144029", "user": {"id": "user-1", "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "accountNumber": "**********"}, "subject": "duno", "priority": "medium", "status": "open", "messages": [{"_id": "msg_1749264144029", "sender": {"id": "user-1", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "yeah\n", "type": "text", "createdAt": "2025-06-07T02:42:24.029Z"}, {"_id": "msg_1749264146041_agent", "sender": {"id": "agent-1", "firstName": "Support", "lastName": "Agent", "role": "agent"}, "content": "Hello! Thank you for contacting SecureBank support. An agent will join you shortly to assist with your inquiry. Please feel free to provide any additional details while you wait.", "type": "text", "createdAt": "2025-06-07T02:42:26.041Z"}], "createdAt": "2025-06-07T02:42:24.029Z", "lastActivity": "2025-06-07T02:42:26.041Z"}], "balance": 2500, "otpData": null}, "user-*************": {"transactions": [{"_id": "txn_1749258677449", "type": "withdrawal", "amount": 2500, "description": "maintenance ", "status": "completed", "reference": "***************", "createdAt": "2025-06-07T01:11:17.449Z"}, {"_id": "default_1_user-*************", "type": "admin_adjustment", "amount": 1000, "description": "Welcome bonus", "status": "completed", "reference": "WB*************", "createdAt": "2025-06-06T01:10:21.754Z"}, {"_id": "default_2_user-*************", "type": "deposit", "amount": 1500, "description": "Initial deposit", "status": "completed", "reference": "DEP*************", "createdAt": "2025-06-05T01:10:21.754Z"}], "notifications": [{"_id": "notif_1749258677450_success", "message": "Withdrawal of $2500 has been processed successfully. Transaction ID: ***************", "type": "success", "read": false, "createdAt": "2025-06-07T01:11:17.450Z"}, {"_id": "notif_1749258669122", "message": "Your withdrawal OTP is: 856443. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T01:11:09.122Z"}, {"_id": "welcome_user-*************", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-07T01:10:21.754Z"}], "chats": [{"_id": "chat_1749258645922", "user": {"id": "user-*************", "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "accountNumber": "**********"}, "subject": "ko", "priority": "medium", "status": "open", "messages": [{"_id": "msg_1749258645922", "sender": {"id": "user-*************", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "yo", "type": "text", "createdAt": "2025-06-07T01:10:45.922Z"}, {"_id": "msg_1749258647923_agent", "sender": {"id": "agent-1", "firstName": "Support", "lastName": "Agent", "role": "agent"}, "content": "Hello! Thank you for contacting SecureBank support. An agent will join you shortly to assist with your inquiry. Please feel free to provide any additional details while you wait.", "type": "text", "createdAt": "2025-06-07T01:10:47.923Z"}, {"_id": "msg_admin_1749258758328", "sender": {"id": "admin-1", "firstName": "Support", "lastName": "Agent", "role": "admin"}, "content": "hey", "type": "text", "createdAt": "2025-06-07T01:12:38.328Z"}], "createdAt": "2025-06-07T01:10:45.922Z", "lastActivity": "2025-06-07T01:12:38.329Z"}], "balance": 0, "otpData": {"otp": "856443", "amount": 2500, "timestamp": *************, "used": true}}, "user-*************": {"transactions": [{"_id": "txn_admin_1749259656688", "type": "admin_adjustment", "amount": 50000, "description": "no reason", "status": "completed", "reference": "ADJ1749259656688", "createdAt": "2025-06-07T01:27:36.688Z"}, {"_id": "txn_1749259749855", "type": "withdrawal", "amount": 10000, "description": "maintenance ", "status": "completed", "reference": "***************", "createdAt": "2025-06-07T01:29:09.855Z"}], "notifications": [{"_id": "notif_admin_1749259656688", "message": "Your account balance has been increased by 50000. New balance: $50000.00. Reason: no reason", "type": "success", "read": false, "createdAt": "2025-06-07T01:27:36.688Z"}, {"_id": "welcome_user-*************", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-07T01:25:01.492Z"}, {"_id": "notif_1749259741072", "message": "Your withdrawal OTP is: 427733. This code will expire in 5 minutes.", "type": "info", "read": false, "createdAt": "2025-06-07T01:29:01.072Z"}, {"_id": "notif_1749259749856_success", "message": "Withdrawal of $10000 has been processed successfully. Transaction ID: ***************", "type": "success", "read": false, "createdAt": "2025-06-07T01:29:09.856Z"}], "chats": [], "balance": 40000, "otpData": {"otp": "427733", "amount": 10000, "timestamp": *************, "used": true}}, "user-*************": {"transactions": [{"_id": "txn_admin_1749262031787", "type": "transfer_in", "amount": 7000, "description": "Transfer from cecilia cobe", "sentFrom": "cecilia cobe", "status": "completed", "reference": "TIN1749262031787", "createdAt": "2025-06-07T02:07:11.787Z"}, {"_id": "txn_admin_1749305025903", "type": "transfer_in", "amount": 50000, "description": "Transfer from sammy", "sentFrom": "sammy", "status": "completed", "reference": "TIN1749305025903", "createdAt": "2025-06-07T14:03:45.903Z"}], "notifications": [{"_id": "notif_admin_1749262031787", "message": "Transfer received: $7000.00 from cecilia cobe. Your account balance is now $7000.00.", "type": "success", "read": false, "createdAt": "2025-06-07T02:07:11.787Z"}, {"_id": "welcome_user-*************", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-07T02:05:14.575Z"}, {"_id": "notif_admin_1749305025903", "message": "Transfer received: $50000.00 from sammy. Your account balance is now $57000.00.", "type": "success", "read": false, "createdAt": "2025-06-07T14:03:45.903Z"}], "chats": [{"_id": "chat_1749261943666", "user": {"id": "user-*************", "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "accountNumber": "**********"}, "subject": "money", "priority": "high", "status": "open", "messages": [{"_id": "msg_1749261943666", "sender": {"id": "user-*************", "firstName": "Test", "lastName": "User", "role": "user"}, "content": "Gimme money", "type": "text", "createdAt": "2025-06-07T02:05:43.666Z"}, {"_id": "msg_1749261945674_agent", "sender": {"id": "agent-1", "firstName": "Support", "lastName": "Agent", "role": "agent"}, "content": "Hello! Thank you for contacting SecureBank support. An agent will join you shortly to assist with your inquiry. Please feel free to provide any additional details while you wait.", "type": "text", "createdAt": "2025-06-07T02:05:45.674Z"}], "createdAt": "2025-06-07T02:05:43.666Z", "lastActivity": "2025-06-07T02:05:45.674Z"}], "balance": 57000, "otpData": null}, "admin-1": {"transactions": [], "notifications": [{"_id": "welcome_admin-1", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-07T18:42:27.625Z"}], "chats": [], "balance": 0, "otpData": null}, "null": {"transactions": [], "notifications": [{"_id": "welcome_null", "message": "Welcome to SecureBank! Your account has been successfully created.", "type": "info", "read": false, "createdAt": "2025-06-10T16:48:52.611Z"}], "chats": [], "balance": 0, "otpData": null}}