const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const { supabase, testConnection } = require('./config/supabase.cjs');

const app = express();

// Basic middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// Test Supabase connection on startup
testConnection();

// Utility functions
const generateAccountNumber = () => {
  return Math.floor(********* + Math.random() * *********).toString();
};

const generateTransactionReference = () => {
  return 'TXN' + Date.now() + Math.random().toString(36).substr(2, 5).toUpperCase();
};

const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// JWT token functions
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, { expiresIn: process.env.JWT_EXPIRE || '7d' });
};

const getUserIdFromToken = (req) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  try {
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    return decoded.id;
  } catch (error) {
    return null;
  }
};

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  try {
    const userId = getUserIdFromToken(req);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get user from Supabase
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error || !user) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    req.userId = userId;
    req.user = user;
    next();
  } catch (error) {
    console.error('❌ Authentication middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

// Admin middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  next();
};

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'SecureBank API with Supabase is running',
    timestamp: new Date().toISOString()
  });
});

// Auth Routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { firstName, lastName, email, password, phone } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate unique account number
    let accountNumber;
    let isUnique = false;
    while (!isUnique) {
      accountNumber = generateAccountNumber();
      const { data: existingAccount } = await supabase
        .from('users')
        .select('id')
        .eq('account_number', accountNumber)
        .single();
      
      if (!existingAccount) {
        isUnique = true;
      }
    }

    // Create user
    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert({
        email,
        password_hash: hashedPassword,
        first_name: firstName,
        last_name: lastName,
        phone: phone || null,
        account_number: accountNumber,
        balance: 0,
        role: 'user',
        is_active: true
      })
      .select()
      .single();

    if (createError) {
      throw createError;
    }

    // Create welcome notification
    await supabase
      .from('notifications')
      .insert({
        user_id: newUser.id,
        type: 'success',
        title: 'Welcome to SecureBank!',
        message: `Welcome ${firstName}! Your account has been created successfully. Your account number is ${accountNumber}.`
      });

    // Generate token
    const token = generateToken(newUser.id);

    // Return user data (without password)
    const { password_hash, ...userWithoutPassword } = newUser;
    const userData = {
      ...userWithoutPassword,
      firstName: newUser.first_name,
      lastName: newUser.last_name,
      accountNumber: newUser.account_number,
      isActive: newUser.is_active,
      profileImage: newUser.profile_image,
      lastLogin: newUser.last_login,
      createdAt: newUser.created_at,
      updatedAt: newUser.updated_at
    };

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        token,
        user: userData
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // Get user from database
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Update last login
    await supabase
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id);

    // Generate token
    const token = generateToken(user.id);

    // Return user data (without password)
    const { password_hash, ...userWithoutPassword } = user;
    const userData = {
      ...userWithoutPassword,
      firstName: user.first_name,
      lastName: user.last_name,
      accountNumber: user.account_number,
      isActive: user.is_active,
      profileImage: user.profile_image,
      lastLogin: new Date().toISOString(),
      createdAt: user.created_at,
      updatedAt: user.updated_at
    };

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        user: userData
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

// Get current user
app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    const { password_hash, ...userWithoutPassword } = req.user;
    const userData = {
      ...userWithoutPassword,
      firstName: req.user.first_name,
      lastName: req.user.last_name,
      accountNumber: req.user.account_number,
      isActive: req.user.is_active,
      profileImage: req.user.profile_image,
      lastLogin: req.user.last_login,
      createdAt: req.user.created_at,
      updatedAt: req.user.updated_at
    };

    res.json({
      success: true,
      data: { user: userData }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user data'
    });
  }
});

// User Routes
app.get('/api/user/profile', authenticateToken, async (req, res) => {
  try {
    const { password_hash, ...userWithoutPassword } = req.user;
    const userData = {
      ...userWithoutPassword,
      firstName: req.user.first_name,
      lastName: req.user.last_name,
      accountNumber: req.user.account_number,
      isActive: req.user.is_active,
      profileImage: req.user.profile_image,
      lastLogin: req.user.last_login,
      createdAt: req.user.created_at,
      updatedAt: req.user.updated_at
    };

    res.json({
      success: true,
      data: { user: userData }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile'
    });
  }
});

app.put('/api/user/profile', authenticateToken, async (req, res) => {
  try {
    const { firstName, lastName, phone, profileImage } = req.body;

    const updateData = {};
    if (firstName) updateData.first_name = firstName;
    if (lastName) updateData.last_name = lastName;
    if (phone !== undefined) updateData.phone = phone;
    if (profileImage !== undefined) updateData.profile_image = profileImage;

    updateData.updated_at = new Date().toISOString();

    const { data: updatedUser, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', req.userId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    const { password_hash, ...userWithoutPassword } = updatedUser;
    const userData = {
      ...userWithoutPassword,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      accountNumber: updatedUser.account_number,
      isActive: updatedUser.is_active,
      profileImage: updatedUser.profile_image,
      lastLogin: updatedUser.last_login,
      createdAt: updatedUser.created_at,
      updatedAt: updatedUser.updated_at
    };

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: userData }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile'
    });
  }
});

app.get('/api/user/balance', authenticateToken, async (req, res) => {
  try {
    res.json({
      success: true,
      data: { balance: req.user.balance }
    });
  } catch (error) {
    console.error('Get balance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get balance'
    });
  }
});

app.get('/api/user/transactions', authenticateToken, async (req, res) => {
  try {
    const { data: transactions, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Transform data to match frontend expectations
    const transformedTransactions = transactions.map(t => ({
      id: t.id,
      type: t.type,
      amount: t.amount,
      reference: t.reference,
      description: t.description,
      recipientName: t.recipient_name,
      recipientAccount: t.recipient_account,
      purpose: t.purpose,
      status: t.status,
      sentFrom: t.sent_from,
      createdAt: t.created_at,
      updatedAt: t.updated_at
    }));

    res.json({
      success: true,
      data: { transactions: transformedTransactions }
    });
  } catch (error) {
    console.error('Get transactions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transactions'
    });
  }
});

app.get('/api/user/notifications', authenticateToken, async (req, res) => {
  try {
    const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Transform data to match frontend expectations
    const transformedNotifications = notifications.map(n => ({
      _id: n.id,
      type: n.type,
      title: n.title,
      message: n.message,
      read: n.read,
      createdAt: n.created_at,
      updatedAt: n.updated_at
    }));

    res.json({
      success: true,
      data: { notifications: transformedNotifications }
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notifications'
    });
  }
});

app.put('/api/user/notifications/:id/read', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('notifications')
      .update({ read: true, updated_at: new Date().toISOString() })
      .eq('id', id)
      .eq('user_id', req.userId);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Mark notification read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read'
    });
  }
});

app.put('/api/user/notifications/read-all', authenticateToken, async (req, res) => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true, updated_at: new Date().toISOString() })
      .eq('user_id', req.userId)
      .eq('read', false);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Mark all notifications read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark all notifications as read'
    });
  }
});

// Transaction Routes
app.post('/api/transactions/withdrawal/request-otp', authenticateToken, async (req, res) => {
  try {
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid amount is required'
      });
    }

    if (amount > req.user.balance) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }

    // Generate OTP
    const otpCode = generateOTP();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    // Store OTP in database
    const { error: otpError } = await supabase
      .from('otps')
      .insert({
        user_id: req.userId,
        otp_code: otpCode,
        purpose: 'withdrawal',
        amount: amount,
        expires_at: expiresAt.toISOString(),
        used: false
      });

    if (otpError) {
      throw otpError;
    }

    // Create notification
    await supabase
      .from('notifications')
      .insert({
        user_id: req.userId,
        type: 'info',
        title: 'Withdrawal OTP',
        message: `Your withdrawal OTP is: ${otpCode}. This code will expire in 5 minutes.`
      });

    res.json({
      success: true,
      message: 'OTP sent successfully',
      data: { otpSent: true }
    });

  } catch (error) {
    console.error('Request withdrawal OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to request OTP'
    });
  }
});

app.post('/api/transactions/withdrawal/process', authenticateToken, async (req, res) => {
  try {
    const { amount, otp, description } = req.body;

    if (!amount || !otp || !description) {
      return res.status(400).json({
        success: false,
        message: 'Amount, OTP, and description are required'
      });
    }

    // Verify OTP
    const { data: otpRecord, error: otpError } = await supabase
      .from('otps')
      .select('*')
      .eq('user_id', req.userId)
      .eq('otp_code', otp)
      .eq('purpose', 'withdrawal')
      .eq('amount', amount)
      .eq('used', false)
      .gte('expires_at', new Date().toISOString())
      .single();

    if (otpError || !otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    // Check balance again
    const { data: currentUser } = await supabase
      .from('users')
      .select('balance')
      .eq('id', req.userId)
      .single();

    if (amount > currentUser.balance) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }

    // Generate transaction reference
    const reference = generateTransactionReference();

    // Start transaction
    const newBalance = currentUser.balance - amount;

    // Update user balance
    const { error: balanceError } = await supabase
      .from('users')
      .update({ balance: newBalance })
      .eq('id', req.userId);

    if (balanceError) {
      throw balanceError;
    }

    // Create transaction record
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: req.userId,
        type: 'withdrawal',
        amount: amount,
        reference: reference,
        description: description,
        status: 'completed'
      });

    if (transactionError) {
      throw transactionError;
    }

    // Mark OTP as used
    await supabase
      .from('otps')
      .update({ used: true })
      .eq('id', otpRecord.id);

    // Create success notification
    await supabase
      .from('notifications')
      .insert({
        user_id: req.userId,
        type: 'success',
        title: 'Withdrawal Successful',
        message: `Withdrawal of $${amount} has been processed successfully. Transaction ID: ${reference}`
      });

    res.json({
      success: true,
      message: 'Withdrawal processed successfully',
      data: {
        transaction: {
          reference,
          amount,
          type: 'withdrawal',
          description,
          status: 'completed',
          createdAt: new Date().toISOString()
        },
        newBalance
      }
    });

  } catch (error) {
    console.error('Process withdrawal error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process withdrawal'
    });
  }
});

// Admin Routes
app.get('/api/admin/dashboard', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Get user statistics
    const { data: users } = await supabase.from('users').select('*');
    const { data: transactions } = await supabase.from('transactions').select('*');
    const { data: chatMessages } = await supabase.from('chat_messages').select('*');

    // Filter out admin users from stats
    const regularUsers = users?.filter(u => u.role !== 'admin') || [];
    const totalUsers = regularUsers.length;
    const activeUsers = regularUsers.filter(u => u.is_active).length;

    // Calculate balances
    const totalBalance = regularUsers.reduce((sum, u) => sum + (u.balance || 0), 0);
    const averageBalance = totalUsers > 0 ? totalBalance / totalUsers : 0;

    // Transaction statistics
    const totalTransactions = transactions?.length || 0;
    const totalVolume = transactions?.reduce((sum, t) => sum + (t.amount || 0), 0) || 0;

    // Calculate new users today
    const today = new Date().toDateString();
    const newUsersToday = regularUsers.filter(u => {
      const userDate = new Date(u.created_at).toDateString();
      return userDate === today;
    }).length;

    // Calculate open chats
    const uniqueUserChats = new Set(chatMessages?.map(m => m.user_id) || []);
    const openChats = uniqueUserChats.size;

    // Get recent transactions with user info
    const recentTransactions = transactions
      ?.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      ?.slice(0, 10)
      ?.map(t => {
        const user = users?.find(u => u.id === t.user_id);
        return {
          id: t.id,
          type: t.type,
          amount: t.amount,
          reference: t.reference,
          description: t.description,
          status: t.status,
          createdAt: t.created_at,
          user: user ? {
            id: user.id,
            firstName: user.first_name,
            lastName: user.last_name,
            email: user.email,
            accountNumber: user.account_number
          } : null
        };
      }) || [];

    const stats = {
      users: {
        total: totalUsers,
        active: activeUsers,
        newToday: newUsersToday
      },
      balance: {
        total: totalBalance,
        average: averageBalance
      },
      transactions: {
        total: totalTransactions,
        volume: totalVolume
      },
      chats: {
        open: openChats
      }
    };

    res.json({
      success: true,
      data: {
        stats,
        recentTransactions
      }
    });

  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load dashboard data'
    });
  }
});

app.get('/api/admin/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Transform data to match frontend expectations
    const transformedUsers = users.map(u => ({
      id: u.id,
      email: u.email,
      firstName: u.first_name,
      lastName: u.last_name,
      phone: u.phone,
      accountNumber: u.account_number,
      balance: u.balance,
      role: u.role,
      isActive: u.is_active,
      profileImage: u.profile_image,
      lastLogin: u.last_login,
      createdAt: u.created_at,
      updatedAt: u.updated_at
    }));

    res.json({
      success: true,
      data: { users: transformedUsers }
    });

  } catch (error) {
    console.error('Get admin users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get users'
    });
  }
});

app.post('/api/admin/users/:userId/adjust-balance', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, sentFrom } = req.body;

    if (!amount || !sentFrom) {
      return res.status(400).json({
        success: false,
        message: 'Amount and sender name are required'
      });
    }

    // Get current user
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate new balance
    const newBalance = user.balance + amount;

    // Update user balance
    const { error: balanceError } = await supabase
      .from('users')
      .update({ balance: newBalance })
      .eq('id', userId);

    if (balanceError) {
      throw balanceError;
    }

    // Create transaction record
    const reference = generateTransactionReference();
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: userId,
        type: amount > 0 ? 'transfer_in' : 'transfer_out',
        amount: Math.abs(amount),
        reference: reference,
        description: `Transfer from ${sentFrom}`,
        sent_from: sentFrom,
        status: 'completed'
      });

    if (transactionError) {
      throw transactionError;
    }

    // Create notification
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        type: 'success',
        title: 'Balance Updated',
        message: `Transfer received: $${Math.abs(amount).toFixed(2)} from ${sentFrom}. Your account balance is now $${newBalance.toFixed(2)}.`
      });

    res.json({
      success: true,
      message: 'Balance adjusted successfully',
      data: {
        newBalance,
        transaction: {
          reference,
          amount: Math.abs(amount),
          type: amount > 0 ? 'transfer_in' : 'transfer_out',
          sentFrom,
          createdAt: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Adjust balance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to adjust balance'
    });
  }
});

// Chat Routes
app.post('/api/chat', authenticateToken, async (req, res) => {
  try {
    const { subject, message, priority = 'medium' } = req.body;

    if (!subject || !message) {
      return res.status(400).json({
        success: false,
        message: 'Subject and message are required'
      });
    }

    // Insert initial message
    const { data: newMessage, error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: req.userId,
        sender_type: 'user',
        content: message.trim(),
        status: 'open'
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Create auto-response message
    const autoResponse = "Thank you for contacting SecureBank support. An agent will join this conversation shortly to assist you.";

    const { data: autoMessage, error: autoError } = await supabase
      .from('chat_messages')
      .insert({
        user_id: req.userId,
        sender_type: 'admin',
        content: autoResponse,
        status: 'open'
      })
      .select()
      .single();

    if (autoError) {
      console.error('Auto-response error:', autoError);
    }

    // Transform data to match frontend expectations
    const messages = [
      {
        _id: newMessage.id,
        content: newMessage.content,
        sender: { role: 'user' },
        timestamp: newMessage.created_at
      }
    ];

    if (autoMessage) {
      messages.push({
        _id: autoMessage.id,
        content: autoMessage.content,
        sender: { role: 'admin' },
        timestamp: autoMessage.created_at
      });
    }

    const chat = {
      _id: `chat_${req.userId}`,
      subject: subject,
      user: {
        id: req.user.id,
        firstName: req.user.first_name,
        lastName: req.user.last_name,
        email: req.user.email,
        accountNumber: req.user.account_number
      },
      messages: messages,
      status: 'open',
      priority: priority,
      createdAt: newMessage.created_at,
      lastActivity: autoMessage ? autoMessage.created_at : newMessage.created_at
    };

    res.json({
      success: true,
      message: 'Chat created successfully',
      data: { chat }
    });

  } catch (error) {
    console.error('Create chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create chat'
    });
  }
});

app.get('/api/chat', authenticateToken, async (req, res) => {
  try {
    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    if (messages.length === 0) {
      return res.json({
        success: true,
        data: { chats: [] }
      });
    }

    // Group messages into a single chat
    const transformedMessages = messages.map(m => ({
      _id: m.id,
      content: m.content,
      sender: { role: m.sender_type },
      timestamp: m.created_at
    }));

    const chat = {
      _id: `chat_${req.userId}`,
      subject: 'Support Chat',
      user: {
        id: req.user.id,
        firstName: req.user.first_name,
        lastName: req.user.last_name,
        email: req.user.email,
        accountNumber: req.user.account_number
      },
      messages: transformedMessages,
      status: 'open',
      priority: 'medium',
      createdAt: messages[0].created_at,
      lastActivity: messages[messages.length - 1].created_at
    };

    res.json({
      success: true,
      data: { chats: [chat] }
    });

  } catch (error) {
    console.error('Get chats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get chats'
    });
  }
});

app.post('/api/chat/:id/messages', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content || !content.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Message content is required'
      });
    }

    // Insert message
    const { data: newMessage, error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: req.userId,
        sender_type: 'user',
        content: content.trim(),
        status: 'open'
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Transform data to match frontend expectations
    const transformedMessage = {
      _id: newMessage.id,
      content: newMessage.content,
      sender: { role: 'user' },
      timestamp: newMessage.created_at,
      status: newMessage.status
    };

    res.json({
      success: true,
      message: 'Message sent successfully',
      data: { message: transformedMessage }
    });

  } catch (error) {
    console.error('Send chat message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message'
    });
  }
});

app.delete('/api/chat/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Extract user ID from chat ID (format: chat_userId)
    const userId = id.replace('chat_', '');

    // Verify this is the user's chat
    if (userId !== req.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Delete all chat messages for this user
    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: 'Chat ended successfully'
    });

  } catch (error) {
    console.error('End chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to end chat'
    });
  }
});

// Admin Chat Routes
app.get('/api/admin/chats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Get all chat messages grouped by user
    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        users!inner(id, first_name, last_name, email, account_number)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Group messages by user
    const chatsByUser = {};
    messages.forEach(message => {
      const userId = message.user_id;
      if (!chatsByUser[userId]) {
        chatsByUser[userId] = {
          _id: `chat_${userId}`,
          userId: userId,
          subject: 'Support Chat',
          user: {
            id: message.users.id,
            firstName: message.users.first_name,
            lastName: message.users.last_name,
            email: message.users.email,
            accountNumber: message.users.account_number
          },
          messages: [],
          lastMessage: null,
          lastActivity: null,
          status: 'open',
          priority: 'medium'
        };
      }

      const transformedMessage = {
        _id: message.id,
        content: message.content,
        sender: { role: message.sender_type },
        timestamp: message.created_at
      };

      chatsByUser[userId].messages.push(transformedMessage);

      // Set last message and activity (most recent)
      if (!chatsByUser[userId].lastMessage ||
          new Date(message.created_at) > new Date(chatsByUser[userId].lastMessage.timestamp)) {
        chatsByUser[userId].lastMessage = transformedMessage;
        chatsByUser[userId].lastActivity = message.created_at;
      }
    });

    // Convert to array and sort by last message time
    const chats = Object.values(chatsByUser).sort((a, b) => {
      if (!a.lastActivity) return 1;
      if (!b.lastActivity) return -1;
      return new Date(b.lastActivity) - new Date(a.lastActivity);
    });

    res.json({
      success: true,
      data: { chats }
    });

  } catch (error) {
    console.error('Get admin chats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get chats'
    });
  }
});

app.post('/api/admin/chats/:chatId/reply', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { chatId } = req.params;
    const { content } = req.body;

    if (!content || !content.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Message content is required'
      });
    }

    // Extract user ID from chat ID (format: chat_userId)
    const userId = chatId.replace('chat_', '');

    // Insert admin reply
    const { data: newMessage, error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: userId,
        sender_type: 'admin',
        content: content.trim(),
        status: 'open'
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Create notification for user
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        type: 'info',
        title: 'New Message from Support',
        message: 'You have received a new message from our support team.'
      });

    // Transform data to match frontend expectations
    const transformedMessage = {
      _id: newMessage.id,
      content: newMessage.content,
      sender: { role: 'admin' },
      timestamp: newMessage.created_at,
      status: newMessage.status
    };

    res.json({
      success: true,
      message: 'Reply sent successfully',
      data: { newMessage: transformedMessage }
    });

  } catch (error) {
    console.error('Send admin reply error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send reply'
    });
  }
});

// Transfer Routes
app.post('/api/transactions/transfer/request-otp', authenticateToken, async (req, res) => {
  try {
    const { amount, recipientName, accountNumber, purpose } = req.body;

    if (!amount || !recipientName || !accountNumber || !purpose) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Amount must be greater than 0'
      });
    }

    if (amount > req.user.balance) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }

    // Generate OTP
    const otpCode = generateOTP();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    // Store OTP in database
    const { error: otpError } = await supabase
      .from('otps')
      .insert({
        user_id: req.userId,
        otp_code: otpCode,
        purpose: 'transfer',
        amount: amount,
        expires_at: expiresAt.toISOString(),
        used: false
      });

    if (otpError) {
      throw otpError;
    }

    // Create notification
    await supabase
      .from('notifications')
      .insert({
        user_id: req.userId,
        type: 'info',
        title: 'Transfer OTP',
        message: `Your transfer OTP is: ${otpCode}. This code will expire in 5 minutes.`
      });

    res.json({
      success: true,
      message: 'OTP sent successfully',
      data: { otpSent: true }
    });

  } catch (error) {
    console.error('Request transfer OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to request OTP'
    });
  }
});

app.post('/api/transactions/transfer/process', authenticateToken, async (req, res) => {
  try {
    const { amount, recipientName, accountNumber, purpose, otp } = req.body;

    if (!amount || !recipientName || !accountNumber || !purpose || !otp) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Verify OTP
    const { data: otpRecord, error: otpError } = await supabase
      .from('otps')
      .select('*')
      .eq('user_id', req.userId)
      .eq('otp_code', otp)
      .eq('purpose', 'transfer')
      .eq('amount', amount)
      .eq('used', false)
      .gte('expires_at', new Date().toISOString())
      .single();

    if (otpError || !otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    // Check balance again
    const { data: currentUser } = await supabase
      .from('users')
      .select('balance')
      .eq('id', req.userId)
      .single();

    if (amount > currentUser.balance) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient balance'
      });
    }

    // Generate transaction reference
    const reference = generateTransactionReference();

    // Start transaction
    const newBalance = currentUser.balance - amount;

    // Update user balance
    const { error: balanceError } = await supabase
      .from('users')
      .update({ balance: newBalance })
      .eq('id', req.userId);

    if (balanceError) {
      throw balanceError;
    }

    // Create transaction record
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: req.userId,
        type: 'transfer_out',
        amount: amount,
        reference: reference,
        description: `Transfer to ${recipientName}`,
        recipient_name: recipientName,
        recipient_account: accountNumber,
        purpose: purpose,
        status: 'completed'
      });

    if (transactionError) {
      throw transactionError;
    }

    // Mark OTP as used
    await supabase
      .from('otps')
      .update({ used: true })
      .eq('id', otpRecord.id);

    // Create success notification
    await supabase
      .from('notifications')
      .insert({
        user_id: req.userId,
        type: 'success',
        title: 'Transfer Successful',
        message: `Transfer of $${amount} to ${recipientName} has been processed successfully. Transaction ID: ${reference}`
      });

    res.json({
      success: true,
      message: 'Transfer processed successfully',
      data: {
        transaction: {
          reference,
          amount,
          type: 'transfer_out',
          recipientName,
          recipientAccount: accountNumber,
          purpose,
          status: 'completed',
          createdAt: new Date().toISOString()
        },
        newBalance
      }
    });

  } catch (error) {
    console.error('Process transfer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process transfer'
    });
  }
});

// Start server
const PORT = process.env.PORT || 3002;
app.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 SecureBank API with Supabase running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
