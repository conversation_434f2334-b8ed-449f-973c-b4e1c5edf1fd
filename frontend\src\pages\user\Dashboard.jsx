import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { userAPI, transactionAPI } from '../../services/api';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const { user, updateUser } = useAuth();
  const [balance, setBalance] = useState(user?.balance || 0);
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);


  useEffect(() => {
    fetchDashboardData();
  }, []);





  // Force refresh when component mounts or user changes
  useEffect(() => {
    if (user?.id) {
      // console.log('🔄 User ID changed, refreshing dashboard...');
      fetchDashboardData();
    }
  }, [user?.id]);

  // Refresh dashboard when window gains focus (user returns from profile page)
  useEffect(() => {
    const handleFocus = () => {
      // console.log('🔄 Window focused, refreshing dashboard data...');
      fetchDashboardData();
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // console.log('🔄 Page became visible, refreshing dashboard data...');
        fetchDashboardData();
      }
    };

    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      // console.log('🔄 Fetching dashboard data...');

      // Fetch updated user profile (includes profile image)
      const profileResponse = await userAPI.getProfile();
      if (profileResponse.data.success) {
        // console.log('👤 Profile data received:', profileResponse.data.user.profileImage ? 'Image present' : 'No image');
        updateUser(profileResponse.data.user);
      }

      // Fetch balance
      const balanceResponse = await userAPI.getBalance();
      setBalance(balanceResponse.data.balance);

      // Fetch recent transactions
      const transactionsResponse = await userAPI.getTransactions({ limit: 5 });
      setRecentTransactions(transactionsResponse.data.transactions);

      // Fetch notifications
      const notificationsResponse = await userAPI.getNotifications();
      setNotifications(notificationsResponse.data.notifications.slice(0, 3));

      // console.log('✅ Dashboard data loaded successfully');

    } catch (error) {
      console.error('❌ Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'withdrawal':
        return '↓';
      case 'deposit':
        return '↑';
      case 'transfer_out':
        return '→';
      case 'transfer_in':
        return '←';
      case 'admin_adjustment':
        return '⚙️';
      default:
        return '•';
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'withdrawal':
        return 'text-red-600';
      case 'transfer_out':
        return 'text-red-600';
      case 'deposit':
        return 'text-green-600';
      case 'transfer_in':
        return 'text-green-600';
      case 'admin_adjustment':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-4 px-4 sm:py-6 sm:px-6 lg:px-8">


        {/* Header */}
        <div className="py-4 sm:py-6">
          <div className="flex items-center space-x-4">
            {/* Profile Image */}
            <div className="flex-shrink-0">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full overflow-hidden bg-gray-200">
                {user?.profileImage && user.profileImage !== 'null' && user.profileImage !== '' ? (
                  <img
                    src={user.profileImage}
                    alt={`${user.firstName} ${user.lastName}`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-blue-100">
                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
              </div>

            </div>

            {/* User Info */}
            <div className="flex-1 min-w-0">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                Welcome back, {user?.firstName}!
              </h1>
              <p className="mt-1 sm:mt-2 text-sm sm:text-base text-gray-600">
                Account Number: {user?.accountNumber}
              </p>
            </div>


          </div>
        </div>

        {/* Balance Card */}
        <div className="mb-6 sm:mb-8">
          <Card className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                <div>
                  <p className="text-primary-100 text-xs sm:text-sm font-medium">Current Balance</p>
                  <p className="text-2xl sm:text-3xl font-bold">{formatCurrency(balance)}</p>
                </div>
                <div className="text-left sm:text-right">
                  <p className="text-primary-100 text-xs sm:text-sm">Last Updated</p>
                  <p className="text-xs sm:text-sm">{formatDate(new Date())}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mb-6 sm:mb-8">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Quick Actions</h2>
          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 dashboard-quick-actions">
          

            <Link to="/transfer">
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-3 sm:p-4 lg:p-6 text-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 lg:mb-4">
                    <span className="text-blue-600 text-sm sm:text-lg lg:text-xl">→</span>
                  </div>
                  <h3 className="text-xs sm:text-sm lg:text-base font-semibold text-gray-900">Transfer</h3>
                  <p className="text-xs text-gray-600 mt-1 hidden sm:block">Send money securely</p>
                </CardContent>
              </Card>
            </Link>

            <Link to="/transactions">
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-3 sm:p-4 lg:p-6 text-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 lg:mb-4">
                    <span className="text-blue-600 text-sm sm:text-lg lg:text-xl">📊</span>
                  </div>
                  <h3 className="text-xs sm:text-sm lg:text-base font-semibold text-gray-900">Transactions</h3>
                  <p className="text-xs text-gray-600 mt-1 hidden sm:block">Transaction history</p>
                </CardContent>
              </Card>
            </Link>

            <Link to="/profile">
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-3 sm:p-4 lg:p-6 text-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 lg:mb-4">
                    <span className="text-purple-600 text-sm sm:text-lg lg:text-xl">👤</span>
                  </div>
                  <h3 className="text-xs sm:text-sm lg:text-base font-semibold text-gray-900">Profile</h3>
                  <p className="text-xs text-gray-600 mt-1 hidden sm:block">Manage your profile</p>
                </CardContent>
              </Card>
            </Link>

            <Link to="/support">
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-3 sm:p-4 lg:p-6 text-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3 lg:mb-4">
                    <span className="text-green-600 text-sm sm:text-lg lg:text-xl">💬</span>
                  </div>
                  <h3 className="text-xs sm:text-sm lg:text-base font-semibold text-gray-900">Get Support</h3>
                  <p className="text-xs text-gray-600 mt-1 hidden sm:block">Live chat support</p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
          {/* Recent Transactions */}
          <Card>
            <CardHeader className="px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm sm:text-base lg:text-lg">Recent Transactions</CardTitle>
                <Link to="/transactions">
                  <Button variant="ghost" size="sm" className="text-xs px-2 py-1">View All</Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent className="px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
              {recentTransactions.length > 0 ? (
                <div className="space-y-2 sm:space-y-3">
                  {recentTransactions.map((transaction) => (
                    <div key={transaction.id || transaction._id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <div className="flex items-center space-x-2 min-w-0 flex-1">
                        <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center ${getTransactionColor(transaction.type)} bg-gray-100 flex-shrink-0`}>
                          <span className="text-xs sm:text-sm">{getTransactionIcon(transaction.type)}</span>
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="text-xs sm:text-sm font-medium text-gray-900 capitalize truncate">{transaction.type.replace('_', ' ')}</p>
                          <p className="text-xs text-gray-600">{formatDate(transaction.createdAt)}</p>
                        </div>
                      </div>
                      <div className="text-right flex-shrink-0 ml-2">
                        <p className={`text-xs sm:text-sm font-semibold ${getTransactionColor(transaction.type)}`}>
                          {(transaction.type === 'withdrawal' || transaction.type === 'transfer_out') ? '-' : '+'}{formatCurrency(transaction.amount)}
                        </p>
                        <p className="text-xs text-gray-500 capitalize">{transaction.status}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <div className="text-gray-400 text-3xl sm:text-4xl mb-2">📊</div>
                  <p className="text-xs sm:text-sm text-gray-500">No transactions yet</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader className="px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm sm:text-base lg:text-lg">Recent Notifications</CardTitle>
                <Link to="/notifications">
                  <Button variant="ghost" size="sm" className="text-xs px-2 py-1">View All</Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent className="px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
              {notifications.length > 0 ? (
                <div className="space-y-2 sm:space-y-3">
                  {notifications.map((notification) => (
                    <div key={notification.id || notification._id} className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-start space-x-2">
                        <div className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full mt-1.5 sm:mt-2 flex-shrink-0 ${notification.read ? 'bg-gray-400' : 'bg-primary-600'}`}></div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs sm:text-sm text-gray-900 break-words leading-relaxed">{notification.message}</p>
                          <p className="text-xs text-gray-500 mt-1">{formatDate(notification.createdAt)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <div className="text-gray-400 text-3xl sm:text-4xl mb-2">🔔</div>
                  <p className="text-xs sm:text-sm text-gray-500">No notifications</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
