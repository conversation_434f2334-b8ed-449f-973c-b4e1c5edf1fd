const jwt = require('jsonwebtoken');

// The token from the login response
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJkY2Q1MGQxMC1iNzllLTQ1MjMtOTExNC0zMGFjYjEwYTUxZTYiLCJpYXQiOjE3NDk1NzA0NTYsImV4cCI6MTc1MDE3NTI1Nn0.9_MD1v6rCQW_wsHmAUh-EGMCs32mzy9QgQKGpaQMQw0';

try {
  // Decode without verification to see the payload
  const decoded = jwt.decode(token);
  console.log('JWT Payload:', JSON.stringify(decoded, null, 2));
  
  // Check what property exists
  if (decoded.userId) {
    console.log('❌ Token contains "userId":', decoded.userId);
  }
  if (decoded.id) {
    console.log('✅ Token contains "id":', decoded.id);
  }
} catch (error) {
  console.error('Error decoding token:', error);
}
