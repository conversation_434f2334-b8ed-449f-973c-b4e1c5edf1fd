const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

console.log('🚀 Starting Simple Supabase Backend...');

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Initialize Supabase
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// JWT functions
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

const getUserIdFromToken = (req) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  try {
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    return decoded.id;
  } catch (error) {
    return null;
  }
};

// Simple auth middleware
const authenticateToken = async (req, res, next) => {
  try {
    const userId = getUserIdFromToken(req);
    if (!userId) {
      return res.status(401).json({ success: false, message: 'Authentication required' });
    }

    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error || !user) {
      return res.status(401).json({ success: false, message: 'User not found' });
    }

    req.userId = userId;
    req.user = user;
    next();
  } catch (error) {
    console.error('Auth error:', error);
    return res.status(500).json({ success: false, message: 'Authentication failed' });
  }
};

// Health check
app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({ 
    success: true, 
    message: 'Simple Supabase Backend is running',
    timestamp: new Date().toISOString()
  });
});

// Login
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('Login attempt for:', req.body.email);
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ success: false, message: 'Email and password required' });
    }

    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !user) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, message: 'Invalid credentials' });
    }

    if (!user.is_active) {
      return res.status(401).json({ success: false, message: 'Account deactivated' });
    }

    const token = generateToken(user.id);
    const { password_hash, ...userWithoutPassword } = user;
    const userData = {
      ...userWithoutPassword,
      firstName: user.first_name,
      lastName: user.last_name,
      accountNumber: user.account_number,
      isActive: user.is_active,
      profileImage: user.profile_image,
      lastLogin: user.last_login,
      createdAt: user.created_at,
      updatedAt: user.updated_at
    };

    console.log('Login successful for:', email);
    res.json({
      success: true,
      message: 'Login successful',
      data: { token, user: userData }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, message: 'Login failed' });
  }
});

// Get user profile
app.get('/api/user/profile', authenticateToken, (req, res) => {
  try {
    const { password_hash, ...userWithoutPassword } = req.user;
    const userData = {
      ...userWithoutPassword,
      firstName: req.user.first_name,
      lastName: req.user.last_name,
      accountNumber: req.user.account_number,
      isActive: req.user.is_active,
      profileImage: req.user.profile_image,
      lastLogin: req.user.last_login,
      createdAt: req.user.created_at,
      updatedAt: req.user.updated_at
    };

    res.json({ success: true, data: { user: userData } });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ success: false, message: 'Failed to get profile' });
  }
});

// Get balance
app.get('/api/user/balance', authenticateToken, (req, res) => {
  try {
    res.json({ success: true, data: { balance: req.user.balance } });
  } catch (error) {
    console.error('Balance error:', error);
    res.status(500).json({ success: false, message: 'Failed to get balance' });
  }
});

// Get transactions
app.get('/api/user/transactions', authenticateToken, async (req, res) => {
  try {
    const { data: transactions, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    const transformedTransactions = transactions.map(t => ({
      id: t.id,
      type: t.type,
      amount: t.amount,
      reference: t.reference,
      description: t.description,
      status: t.status,
      createdAt: t.created_at
    }));

    res.json({ success: true, data: { transactions: transformedTransactions } });
  } catch (error) {
    console.error('Transactions error:', error);
    res.status(500).json({ success: false, message: 'Failed to get transactions' });
  }
});

// Get notifications
app.get('/api/user/notifications', authenticateToken, async (req, res) => {
  try {
    const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    const transformedNotifications = notifications.map(n => ({
      _id: n.id,
      type: n.type,
      title: n.title,
      message: n.message,
      read: n.read,
      createdAt: n.created_at
    }));

    res.json({ success: true, data: { notifications: transformedNotifications } });
  } catch (error) {
    console.error('Notifications error:', error);
    res.status(500).json({ success: false, message: 'Failed to get notifications' });
  }
});

// Get chats
app.get('/api/chat', authenticateToken, async (req, res) => {
  try {
    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: true });

    if (error) throw error;

    if (messages.length === 0) {
      return res.json({ success: true, data: { chats: [] } });
    }

    const transformedMessages = messages.map(m => ({
      _id: m.id,
      content: m.content,
      sender: { role: m.sender_type },
      timestamp: m.created_at
    }));

    const chat = {
      _id: `chat_${req.userId}`,
      subject: 'Support Chat',
      messages: transformedMessages,
      status: 'open',
      priority: 'medium',
      createdAt: messages[0].created_at,
      lastActivity: messages[messages.length - 1].created_at
    };

    res.json({ success: true, data: { chats: [chat] } });
  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({ success: false, message: 'Failed to get chats' });
  }
});

// Send message
app.post('/api/chat/:id/messages', authenticateToken, async (req, res) => {
  try {
    const { content } = req.body;
    if (!content) {
      return res.status(400).json({ success: false, message: 'Content required' });
    }

    const { data: newMessage, error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: req.userId,
        sender_type: 'user',
        content: content.trim(),
        status: 'open'
      })
      .select()
      .single();

    if (error) throw error;

    const transformedMessage = {
      _id: newMessage.id,
      content: newMessage.content,
      sender: { role: 'user' },
      timestamp: newMessage.created_at
    };

    res.json({ success: true, data: { message: transformedMessage } });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ success: false, message: 'Failed to send message' });
  }
});

// Start server
const PORT = 3002;
app.listen(PORT, () => {
  console.log(`✅ Simple Supabase Backend running on port ${PORT}`);
  console.log(`🔗 Health: http://localhost:${PORT}/health`);
});

module.exports = app;
