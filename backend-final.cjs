const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { createServer } = require('http');
const { Server } = require('socket.io');

// console.log('🚀 Starting SecureBank Backend...');

const app = express();

// Basic middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// Request logging (commented out for production)
// app.use((req, res, next) => {
//   console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.path}`);
//   if (req.body && Object.keys(req.body).length > 0) {
//     console.log('Body:', req.body);
//   }
//   next();
// });



// File paths for data persistence
const DATA_DIR = path.join(__dirname, 'data');
const USER_DATA_FILE = path.join(DATA_DIR, 'userData.json');
const USERS_FILE = path.join(DATA_DIR, 'users.json');

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Load data from files
let userData = {};
let users = [];

// Admin notifications for new messages
let adminNotifications = [];

// Load user data
try {
  if (fs.existsSync(USER_DATA_FILE)) {
    const data = fs.readFileSync(USER_DATA_FILE, 'utf8');
    userData = JSON.parse(data);
    // console.log('✅ User data loaded from file');
  }
} catch (error) {
  // console.log('⚠️ Could not load user data, starting fresh');
  userData = {};
}

// Load users
try {
  if (fs.existsSync(USERS_FILE)) {
    const data = fs.readFileSync(USERS_FILE, 'utf8');
    users = JSON.parse(data);
    // console.log('✅ Users loaded from file');
  }
} catch (error) {
  // console.log('⚠️ Could not load users, starting fresh');
  users = [];
}

// Add default admin if no users exist
if (users.length === 0) {
  users.push({
    id: 'admin-1',
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'Admin123!',
    phone: '+**********',
    accountNumber: '**********',
    balance: 0,
    role: 'admin',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date()
  });
  saveUsers();
}

// console.log('📋 Total users loaded:', users.length);
// console.log('👤 Admin user:', users.find(u => u.role === 'admin')?.email || 'Not found');

// Save data functions
const saveUserData = () => {
  try {
    fs.writeFileSync(USER_DATA_FILE, JSON.stringify(userData, null, 2));
  } catch (error) {
    console.error('❌ Error saving user data:', error);
  }
};

const saveUsers = () => {
  try {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
  } catch (error) {
    console.error('❌ Error saving users:', error);
  }
};

// Helper function to get or create user data
const getUserData = (userId) => {
  if (!userData[userId]) {
    userData[userId] = {
      transactions: [], // New users start with no transactions
      notifications: [
        {
          _id: 'welcome_' + userId,
          message: 'Welcome to SecureBank! Your account has been successfully created.',
          type: 'info',
          read: false,
          createdAt: new Date().toISOString()
        }
      ],
      chats: [],
      balance: 0.00, // New users start with zero balance
      otpData: null
    };
    // Save to file when new user data is created
    saveUserData();
  }
  return userData[userId];
};

// Helper function to get user ID from token
const getUserIdFromToken = (req) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return null;
    }

    const decoded = jwt.verify(token, 'your-secret-key');
    return decoded.id;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
};

// Helper functions
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, 'your-secret-key', { expiresIn: '7d' });
};

const generateAccountNumber = () => {
  return Math.floor(********** + Math.random() * **********).toString();
};

// Authentication middleware
const authenticateToken = (req, res, next) => {
  try {
    // console.log('🔐 Authentication middleware called for:', req.method, req.path);
    const userId = getUserIdFromToken(req);
    // console.log('🔐 User ID from token:', userId);

    if (!userId) {
      // console.log('❌ No user ID found in token');
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const user = users.find(u => u.id === userId);
    // console.log('🔐 User found:', user ? user.email : 'Not found');

    if (!user) {
      // console.log('❌ User not found in database');
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    req.userId = userId;
    req.user = user;
    // console.log('✅ Authentication successful for:', user.email);
    next();
  } catch (error) {
    console.error('❌ Authentication middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

// Routes
app.get('/api/health', (req, res) => {
  console.log('✅ Health check requested');
  res.json({
    success: true,
    message: 'SecureBank Backend is running',
    timestamp: new Date().toISOString()
  });
});

// Auth routes
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('🔐 Login request received');
    const { email, password } = req.body;

    if (!email || !password) {
      console.log('❌ Missing credentials');
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // Find user
    console.log('🔍 Looking for user with email:', email);
    console.log('🔍 Total users in database:', users.length);
    console.log('🔍 Available emails:', users.map(u => u.email));

    const user = users.find(u => u.email === email);
    if (!user) {
      console.log('❌ User not found:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    console.log('✅ User found:', user.email, 'Password in DB:', user.password);

    // Check password (handle both hashed and plain text for backward compatibility)
    let isMatch = false;

    if (user.password.startsWith('$2a$') || user.password.startsWith('$2b$')) {
      // Password is hashed with bcrypt
      isMatch = await bcrypt.compare(password, user.password);
    } else {
      // Password is plain text (for existing users)
      isMatch = user.password === password;
    }

    if (!isMatch) {
      console.log('❌ Invalid password for:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login
    user.lastLogin = new Date();

    // Generate token
    const token = generateToken(user.id);

    console.log('✅ Login successful for:', email, 'Role:', user.role);

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        accountNumber: user.accountNumber,
        balance: user.balance,
        role: user.role,
        lastLogin: user.lastLogin,
        profileImage: user.profileImage || null
      }
    });
  } catch (error) {
    console.error('❌ Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login'
    });
  }
});

app.post('/api/auth/register', async (req, res) => {
  try {
    console.log('📝 Registration request received');
    const { firstName, lastName, email, password, phone } = req.body;

    if (!firstName || !lastName || !email || !password || !phone) {
      console.log('❌ Missing required fields');
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Check if user exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      console.log('❌ User already exists:', email);
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email'
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new user
    const newUser = {
      id: 'user-' + Date.now(),
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      accountNumber: generateAccountNumber(),
      balance: 0, // New users start with zero balance
      role: 'user',
      isActive: true,
      lastLogin: new Date(),
      createdAt: new Date()
    };

    users.push(newUser);

    // Initialize user data
    getUserData(newUser.id);

    // Save users to file
    saveUsers();

    // Generate token
    const token = generateToken(newUser.id);

    console.log('✅ User registered successfully:', email);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: newUser.id,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email,
        phone: newUser.phone,
        accountNumber: newUser.accountNumber,
        balance: newUser.balance,
        role: newUser.role
      }
    });
  } catch (error) {
    console.error('❌ Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
});

app.get('/api/auth/me', authenticateToken, (req, res) => {
  console.log('👤 Get current user request');

  const userDataObj = getUserData(req.userId);

  res.json({
    success: true,
    user: {
      id: req.user.id,
      firstName: req.user.firstName,
      lastName: req.user.lastName,
      email: req.user.email,
      phone: req.user.phone,
      accountNumber: req.user.accountNumber,
      balance: userDataObj.balance,
      role: req.user.role,
      lastLogin: req.user.lastLogin,
      createdAt: req.user.createdAt,
      profileImage: req.user.profileImage || null
    }
  });
});

app.post('/api/auth/logout', (req, res) => {
  console.log('👋 Logout request');
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// User routes
app.get('/api/user/profile', authenticateToken, (req, res) => {
  console.log('👤 Profile request');

  const user = users.find(u => u.id === req.userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const userDataObj = getUserData(req.userId);

  res.json({
    success: true,
    user: {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      fullName: `${user.firstName} ${user.lastName}`,
      email: user.email,
      phone: user.phone,
      accountNumber: user.accountNumber,
      balance: userDataObj.balance,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      profileImage: user.profileImage || null
    }
  });
});

// Update profile route
app.put('/api/user/profile', authenticateToken, (req, res) => {
  try {
    console.log('✏️ Profile update request');
    console.log('Request body:', req.body);
    console.log('User ID:', req.userId);

    const { firstName, lastName, phone, profileImage } = req.body;



    if (!firstName || !lastName || !phone) {
      console.log('❌ Missing required fields');
      return res.status(400).json({
        success: false,
        message: 'First name, last name, and phone are required'
      });
    }

    // Validate profile image if provided
    let processedProfileImage = null;
    if (profileImage !== undefined) {
      if (profileImage === null || profileImage === '') {
        // Explicitly clearing the image
        processedProfileImage = null;
      } else if (typeof profileImage === 'string' && profileImage.startsWith('data:image/')) {
        processedProfileImage = profileImage;
      } else {
        return res.status(400).json({
          success: false,
          message: 'Invalid image format. Please provide a valid base64 image.'
        });
      }
    }

    // Find and update user
    const userIndex = users.findIndex(u => u.id === req.userId);
    console.log('User index found:', userIndex);

    if (userIndex === -1) {
      console.log('❌ User not found');
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    console.log('Current user data:', users[userIndex]);

    // Update user data
    const updatedUser = {
      ...users[userIndex],
      firstName,
      lastName,
      phone,
      updatedAt: new Date()
    };

    // Handle profileImage update/clear
    if (processedProfileImage !== null) {
      updatedUser.profileImage = processedProfileImage;
    } else if (profileImage === null || profileImage === '') {
      // Explicitly remove profileImage field when clearing
      delete updatedUser.profileImage;
    }

    users[userIndex] = updatedUser;

    console.log('Updated user data:', users[userIndex]);

    // Save to file
    saveUsers();

    // Also update the user's lastLogin to trigger data refresh
    users[userIndex].lastLogin = new Date();

    console.log('✅ Profile updated for user:', users[userIndex].email);
    console.log('✅ Profile image saved:', users[userIndex].profileImage ? 'Yes' : 'No');

    const responseUser = {
      id: users[userIndex].id,
      firstName: users[userIndex].firstName,
      lastName: users[userIndex].lastName,
      fullName: `${users[userIndex].firstName} ${users[userIndex].lastName}`,
      email: users[userIndex].email,
      phone: users[userIndex].phone,
      accountNumber: users[userIndex].accountNumber,
      profileImage: users[userIndex].profileImage || null, // Always include, even if null
      lastLogin: users[userIndex].lastLogin,
      createdAt: users[userIndex].createdAt,
      updatedAt: users[userIndex].updatedAt
    };



    res.json({
      success: true,
      message: 'Profile updated successfully',
      user: responseUser
    });
  } catch (error) {
    console.error('❌ Profile update error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

app.get('/api/user/balance', (req, res) => {
  console.log('💰 Balance request');

  const userId = getUserIdFromToken(req);
  const user = users.find(u => u.id === userId);
  const userDataObj = getUserData(userId);

  res.json({
    success: true,
    balance: userDataObj.balance,
    accountNumber: user ? user.accountNumber : '**********'
  });
});

app.get('/api/user/transactions', (req, res) => {
  console.log('📊 Transactions request');

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);

  res.json({
    success: true,
    transactions: userDataObj.transactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)),
    pagination: {
      page: 1,
      limit: 10,
      total: userDataObj.transactions.length,
      pages: Math.ceil(userDataObj.transactions.length / 10)
    }
  });
});

app.get('/api/user/notifications', (req, res) => {
  console.log('🔔 Notifications request');

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);

  res.json({
    success: true,
    notifications: userDataObj.notifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  });
});

// Transaction routes
app.post('/api/transactions/withdrawal/request-otp', (req, res) => {
  console.log('💰 Withdrawal OTP request');
  const { amount } = req.body;

  if (!amount || amount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid amount is required'
    });
  }

  // Generate OTP (6 digits)
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);

  // Store OTP temporarily (in real app, use database)
  const otpData = {
    otp,
    amount,
    timestamp: Date.now(),
    used: false
  };

  // Store in user's data
  userDataObj.otpData = otpData;

  console.log('✅ OTP generated:', otp, 'for amount:', amount);

  // Add notification with OTP
  const notification = {
    _id: 'notif_' + Date.now(),
    message: `Your withdrawal OTP is: ${otp}. This code will expire in 5 minutes.`,
    type: 'info',
    read: false,
    createdAt: new Date().toISOString()
  };
  userDataObj.notifications.push(notification);

  // Save user data
  saveUserData();

  res.json({
    success: true,
    message: 'OTP sent to your notifications',
    otp: otp // In development, return OTP for testing
  });
});

app.post('/api/transactions/withdrawal/process', (req, res) => {
  console.log('💸 Process withdrawal request');
  const { amount, otp, description = 'Withdrawal' } = req.body;

  if (!amount || !otp) {
    return res.status(400).json({
      success: false,
      message: 'Amount and OTP are required'
    });
  }

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);

  // Verify OTP
  if (!userDataObj.otpData || userDataObj.otpData.otp !== otp || userDataObj.otpData.used) {
    return res.status(400).json({
      success: false,
      message: 'Invalid or expired OTP'
    });
  }

  // Check if OTP is for the same amount
  if (userDataObj.otpData.amount !== amount) {
    return res.status(400).json({
      success: false,
      message: 'OTP amount mismatch'
    });
  }

  // Check if OTP is not expired (5 minutes)
  if (Date.now() - userDataObj.otpData.timestamp > 5 * 60 * 1000) {
    return res.status(400).json({
      success: false,
      message: 'OTP has expired'
    });
  }

  // Check if user has sufficient balance
  if (userDataObj.balance < amount) {
    return res.status(400).json({
      success: false,
      message: 'Insufficient balance'
    });
  }

  // Mark OTP as used
  userDataObj.otpData.used = true;

  // Create transaction
  const transaction = {
    _id: 'txn_' + Date.now(),
    type: 'withdrawal',
    amount,
    description,
    status: 'completed',
    reference: 'WD' + Date.now(),
    createdAt: new Date().toISOString()
  };

  userDataObj.transactions.push(transaction);

  // Update user balance
  userDataObj.balance -= amount;
  const newBalance = userDataObj.balance;

  console.log('✅ Withdrawal processed:', amount, 'New balance:', newBalance);

  // Add success notification
  const successNotification = {
    _id: 'notif_' + Date.now() + '_success',
    message: `Withdrawal of $${amount} has been processed successfully. Transaction ID: ${transaction.reference}`,
    type: 'success',
    read: false,
    createdAt: new Date().toISOString()
  };
  userDataObj.notifications.push(successNotification);

  // Save user data
  saveUserData();

  res.json({
    success: true,
    message: 'Withdrawal processed successfully',
    transaction,
    newBalance
  });
});

// Transfer routes
app.post('/api/transactions/transfer/request-otp', authenticateToken, (req, res) => {
  console.log('💸 Transfer OTP request');
  const { amount, recipientName, accountNumber, purpose } = req.body;

  if (!amount || amount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Valid amount is required'
    });
  }

  if (!recipientName || !accountNumber || !purpose) {
    return res.status(400).json({
      success: false,
      message: 'Recipient name, account number, and purpose are required'
    });
  }

  // Generate OTP (6 digits)
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);

  // Store OTP temporarily (in real app, use database)
  const otpData = {
    otp,
    amount,
    recipientName,
    accountNumber,
    purpose,
    timestamp: Date.now(),
    used: false
  };

  // Store in user's data
  userDataObj.otpData = otpData;

  console.log('✅ Transfer OTP generated:', otp, 'for amount:', amount);

  // Save user data
  saveUserData();

  res.json({
    success: true,
    message: 'Transfer verification required. Please provide OTP to proceed.'
  });
});

app.post('/api/transactions/transfer/process', authenticateToken, (req, res) => {
  console.log('💸 Process transfer request');
  const { amount, recipientName, accountNumber, purpose, otp } = req.body;

  if (!amount || !otp || !recipientName || !accountNumber || !purpose) {
    return res.status(400).json({
      success: false,
      message: 'All fields including OTP are required'
    });
  }

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);

  // Verify OTP
  if (!userDataObj.otpData || userDataObj.otpData.otp !== otp || userDataObj.otpData.used) {
    return res.status(400).json({
      success: false,
      message: 'Invalid or expired OTP'
    });
  }

  // Check if OTP is for the same transfer details
  if (userDataObj.otpData.amount !== amount ||
      userDataObj.otpData.recipientName !== recipientName ||
      userDataObj.otpData.accountNumber !== accountNumber ||
      userDataObj.otpData.purpose !== purpose) {
    return res.status(400).json({
      success: false,
      message: 'Transfer details do not match OTP request'
    });
  }

  // Check if OTP is not expired (5 minutes)
  if (Date.now() - userDataObj.otpData.timestamp > 5 * 60 * 1000) {
    return res.status(400).json({
      success: false,
      message: 'OTP has expired'
    });
  }

  // Check if user has sufficient balance
  if (userDataObj.balance < amount) {
    return res.status(400).json({
      success: false,
      message: 'Insufficient balance'
    });
  }

  // Mark OTP as used
  userDataObj.otpData.used = true;

  // Create transaction
  const transaction = {
    _id: 'txn_' + Date.now(),
    type: 'transfer_out',
    amount,
    description: `Transfer to ${recipientName} - ${purpose}`,
    recipientName,
    accountNumber,
    purpose,
    status: 'completed',
    reference: 'TRF' + Date.now(),
    createdAt: new Date().toISOString()
  };

  userDataObj.transactions.push(transaction);

  // Update user balance
  userDataObj.balance -= amount;
  const newBalance = userDataObj.balance;

  console.log('✅ Transfer processed:', amount, 'to', recipientName, 'New balance:', newBalance);

  // Add success notification
  const successNotification = {
    _id: 'notif_' + Date.now() + '_success',
    message: `Transfer of $${amount} to ${recipientName} has been processed successfully. Transaction ID: ${transaction.reference}`,
    type: 'success',
    read: false,
    createdAt: new Date().toISOString()
  };
  userDataObj.notifications.push(successNotification);

  // Save user data
  saveUserData();

  res.json({
    success: true,
    message: 'Transfer completed successfully',
    transaction,
    newBalance
  });
});

// Chat routes
app.post('/api/chat', authenticateToken, (req, res) => {
  console.log('💬 Create chat request');
  const { subject, message, priority = 'medium' } = req.body;

  if (!subject || !message) {
    return res.status(400).json({
      success: false,
      message: 'Subject and message are required'
    });
  }

  const userId = getUserIdFromToken(req);
  const user = users.find(u => u.id === userId);
  const userDataObj = getUserData(userId);

  const chat = {
    _id: 'chat_' + Date.now(),
    user: {
      id: userId,
      firstName: user ? user.firstName : 'Unknown',
      lastName: user ? user.lastName : 'User',
      email: user ? user.email : '<EMAIL>',
      accountNumber: user ? user.accountNumber : 'N/A'
    },
    subject,
    priority,
    status: 'open',
    messages: [{
      _id: 'msg_' + Date.now(),
      sender: {
        id: userId,
        firstName: user ? user.firstName : 'Unknown',
        lastName: user ? user.lastName : 'User',
        role: 'user'
      },
      content: message,
      type: 'text',
      createdAt: new Date().toISOString()
    }],
    createdAt: new Date().toISOString(),
    lastActivity: new Date().toISOString()
  };

  userDataObj.chats.push(chat);

  // Create admin notification for new chat
  const adminNotification = {
    _id: 'admin_notif_' + Date.now(),
    type: 'new_chat',
    chatId: chat._id,
    userId: userId,
    user: user ? {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      accountNumber: user.accountNumber
    } : {
      firstName: 'Unknown',
      lastName: 'User',
      email: '<EMAIL>',
      accountNumber: 'N/A'
    },
    message: `New support chat created by ${user ? user.firstName + ' ' + user.lastName : 'Unknown User'}: "${subject}"`,
    content: message,
    priority: priority,
    read: false,
    createdAt: new Date().toISOString()
  };

  adminNotifications.push(adminNotification);

  // Save user data
  saveUserData();

  // Add automatic agent response after 2 seconds
  setTimeout(() => {
    const agentResponse = {
      _id: 'msg_' + Date.now() + '_agent',
      sender: {
        id: 'agent-1',
        firstName: 'Support',
        lastName: 'Agent',
        role: 'agent'
      },
      content: 'Hello! Thank you for contacting SecureBank support. An agent will join you shortly to assist with your inquiry. Please feel free to provide any additional details while you wait.',
      type: 'text',
      createdAt: new Date().toISOString()
    };

    chat.messages.push(agentResponse);
    chat.lastActivity = new Date().toISOString();

    // Save user data after auto-response
    saveUserData();

    console.log('🤖 Auto-response added to chat:', chat._id);
  }, 2000);

  console.log('✅ Chat created:', chat._id);

  res.status(201).json({
    success: true,
    message: 'Chat created successfully',
    chat
  });
});

app.get('/api/chat', authenticateToken, (req, res) => {
  console.log('💬 Get chats request');

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);

  res.json({
    success: true,
    chats: userDataObj.chats.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity))
  });
});

app.get('/api/chat/:id', authenticateToken, (req, res) => {
  console.log('💬 Get chat request:', req.params.id);

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);
  const chat = userDataObj.chats.find(c => c._id === req.params.id);

  if (!chat) {
    return res.status(404).json({
      success: false,
      message: 'Chat not found'
    });
  }

  res.json({
    success: true,
    chat
  });
});

app.post('/api/chat/:id/messages', authenticateToken, (req, res) => {
  console.log('💬 Send message request:', req.params.id);
  const { content } = req.body;

  if (!content) {
    return res.status(400).json({
      success: false,
      message: 'Message content is required'
    });
  }

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);
  const chat = userDataObj.chats.find(c => c._id === req.params.id);

  if (!chat) {
    return res.status(404).json({
      success: false,
      message: 'Chat not found'
    });
  }

  const user = users.find(u => u.id === userId);

  const newMessage = {
    _id: 'msg_' + Date.now(),
    sender: {
      id: userId,
      firstName: user ? user.firstName : 'Unknown',
      lastName: user ? user.lastName : 'User',
      role: 'user'
    },
    content,
    type: 'text',
    createdAt: new Date().toISOString()
  };

  chat.messages.push(newMessage);
  chat.lastActivity = new Date().toISOString();

  // Create admin notification for new user message
  const adminNotification = {
    _id: 'admin_notif_' + Date.now(),
    type: 'new_message',
    chatId: req.params.id,
    userId: userId,
    user: user ? {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      accountNumber: user.accountNumber
    } : {
      firstName: 'Unknown',
      lastName: 'User',
      email: '<EMAIL>',
      accountNumber: 'N/A'
    },
    message: `New message from ${user ? user.firstName + ' ' + user.lastName : 'Unknown User'} in chat: "${chat.subject}"`,
    content: newMessage.content,
    read: false,
    createdAt: new Date().toISOString()
  };

  adminNotifications.push(adminNotification);

  // Save user data
  saveUserData();

  console.log('✅ Message sent to chat:', req.params.id);
  console.log('🔔 Admin notification created for new message');

  res.status(201).json({
    success: true,
    message: 'Message sent successfully',
    newMessage
  });
});

// End chat endpoint
app.delete('/api/chat/:id', authenticateToken, (req, res) => {
  console.log('🔚 End chat request:', req.params.id);

  const userId = getUserIdFromToken(req);
  const userDataObj = getUserData(userId);
  const chatIndex = userDataObj.chats.findIndex(c => c._id === req.params.id);

  if (chatIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Chat not found'
    });
  }

  // Remove chat from array (this clears the history)
  userDataObj.chats.splice(chatIndex, 1);

  // Save user data
  saveUserData();

  console.log('✅ Chat ended and history cleared:', req.params.id);

  res.json({
    success: true,
    message: 'Chat ended successfully'
  });
});

// Admin routes
app.get('/api/admin/dashboard', authenticateToken, (req, res) => {
  console.log('📈 Admin dashboard request');

  // Calculate real statistics from all users
  const totalUsers = users.filter(u => u.role === 'user').length;
  const activeUsers = users.filter(u => u.role === 'user' && u.isActive).length;

  // Calculate total balance across all users
  let totalBalance = 0;
  let totalTransactions = 0;
  let todayTransactions = 0;
  let totalChats = 0;
  let openChats = 0;
  const allTransactions = [];

  const today = new Date().toDateString();

  // Aggregate data from all users
  Object.keys(userData).forEach(userId => {
    const userDataObj = userData[userId];
    totalBalance += userDataObj.balance || 0;
    totalTransactions += userDataObj.transactions.length;
    totalChats += userDataObj.chats.length;

    // Count today's transactions
    userDataObj.transactions.forEach(transaction => {
      if (new Date(transaction.createdAt).toDateString() === today) {
        todayTransactions++;
      }

      // Add user info to transaction for display
      const user = users.find(u => u.id === userId);
      allTransactions.push({
        ...transaction,
        user: user ? {
          firstName: user.firstName,
          lastName: user.lastName,
          accountNumber: user.accountNumber
        } : {
          firstName: 'Unknown',
          lastName: 'User',
          accountNumber: 'N/A'
        }
      });
    });

    // Count open chats
    userDataObj.chats.forEach(chat => {
      if (chat.status === 'open') {
        openChats++;
      }
    });
  });

  // Calculate new users today
  const newToday = users.filter(u =>
    u.role === 'user' &&
    new Date(u.createdAt).toDateString() === today
  ).length;

  // Sort transactions by date (newest first) and get recent ones
  const recentTransactions = allTransactions
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 10);

  res.json({
    success: true,
    stats: {
      users: {
        total: totalUsers,
        active: activeUsers,
        newToday: newToday
      },
      transactions: {
        total: totalTransactions,
        today: todayTransactions
      },
      balance: {
        total: totalBalance,
        average: totalUsers > 0 ? totalBalance / totalUsers : 0
      },
      chats: {
        total: totalChats,
        open: openChats
      }
    },
    recentTransactions
  });
});

// Get all users (admin only)
app.get('/api/admin/users', authenticateToken, (req, res) => {
  console.log('👥 Admin get all users request');

  const userList = users.filter(u => u.role === 'user').map(user => {
    const userDataObj = getUserData(user.id);
    return {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      accountNumber: user.accountNumber,
      balance: userDataObj.balance,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      transactionCount: userDataObj.transactions.length,
      chatCount: userDataObj.chats.length
    };
  });

  res.json({
    success: true,
    users: userList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  });
});

// Get all transactions (admin only)
app.get('/api/admin/transactions', authenticateToken, (req, res) => {
  console.log('📊 Admin get all transactions request');

  const allTransactions = [];

  Object.keys(userData).forEach(userId => {
    const userDataObj = userData[userId];
    const user = users.find(u => u.id === userId);

    userDataObj.transactions.forEach(transaction => {
      allTransactions.push({
        ...transaction,
        userId,
        user: user ? {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          accountNumber: user.accountNumber
        } : {
          firstName: 'Unknown',
          lastName: 'User',
          email: '<EMAIL>',
          accountNumber: 'N/A'
        }
      });
    });
  });

  res.json({
    success: true,
    transactions: allTransactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  });
});

// Get all chats (admin only)
app.get('/api/admin/chats', (req, res) => {
  console.log('💬 Admin get all chats request');

  const allChats = [];

  Object.keys(userData).forEach(userId => {
    const userDataObj = userData[userId];
    const user = users.find(u => u.id === userId);

    userDataObj.chats.forEach(chat => {
      allChats.push({
        ...chat,
        userId,
        user: user ? {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          accountNumber: user.accountNumber
        } : chat.user
      });
    });
  });

  res.json({
    success: true,
    chats: allChats.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity))
  });
});

// Get all notifications (admin only)
app.get('/api/admin/notifications', (req, res) => {
  console.log('🔔 Admin get all notifications request');

  const allNotifications = [];

  Object.keys(userData).forEach(userId => {
    const userDataObj = userData[userId];
    const user = users.find(u => u.id === userId);

    userDataObj.notifications.forEach(notification => {
      allNotifications.push({
        ...notification,
        userId,
        user: user ? {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          accountNumber: user.accountNumber
        } : {
          firstName: 'Unknown',
          lastName: 'User',
          email: '<EMAIL>',
          accountNumber: 'N/A'
        }
      });
    });
  });

  res.json({
    success: true,
    notifications: allNotifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  });
});

// Adjust user balance (admin only)
app.post('/api/admin/users/:userId/adjust-balance', (req, res) => {
  console.log('💰 Admin adjust balance request for user:', req.params.userId);
  const { amount, sentFrom = 'SecureBank Admin' } = req.body;

  if (!amount || isNaN(amount)) {
    return res.status(400).json({
      success: false,
      message: 'Valid amount is required'
    });
  }

  const userId = req.params.userId;
  const user = users.find(u => u.id === userId);

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const userDataObj = getUserData(userId);
  const oldBalance = userDataObj.balance;
  userDataObj.balance += parseFloat(amount);

  // Create transaction record
  const transaction = {
    _id: 'txn_admin_' + Date.now(),
    type: amount > 0 ? 'transfer_in' : 'transfer_out',
    amount: Math.abs(parseFloat(amount)),
    description: `Transfer ${amount > 0 ? 'from' : 'to'} ${sentFrom}`,
    sentFrom: sentFrom,
    status: 'completed',
    reference: (amount > 0 ? 'TIN' : 'TOUT') + Date.now(),
    createdAt: new Date().toISOString()
  };

  userDataObj.transactions.push(transaction);

  // Add notification to user
  const notification = {
    _id: 'notif_admin_' + Date.now(),
    message: amount > 0
      ? `Transfer received: $${Math.abs(amount).toFixed(2)} from ${sentFrom}. Your account balance is now $${userDataObj.balance.toFixed(2)}.`
      : `Transfer sent: $${Math.abs(amount).toFixed(2)} to ${sentFrom}. Your account balance is now $${userDataObj.balance.toFixed(2)}.`,
    type: amount > 0 ? 'success' : 'info',
    read: false,
    createdAt: new Date().toISOString()
  };

  userDataObj.notifications.push(notification);

  // Save data
  saveUserData();

  console.log('✅ Balance adjusted for user:', userId, 'Old:', oldBalance, 'New:', userDataObj.balance);

  res.json({
    success: true,
    message: 'Balance adjusted successfully',
    oldBalance,
    newBalance: userDataObj.balance,
    transaction
  });
});

// Reply to chat (admin only)
app.post('/api/admin/chats/:chatId/reply', (req, res) => {
  console.log('💬 Admin reply to chat:', req.params.chatId);
  const { content } = req.body;

  if (!content) {
    return res.status(400).json({
      success: false,
      message: 'Message content is required'
    });
  }

  // Find the chat across all users
  let foundChat = null;
  let foundUserId = null;

  Object.keys(userData).forEach(userId => {
    const userDataObj = userData[userId];
    const chat = userDataObj.chats.find(c => c._id === req.params.chatId);
    if (chat) {
      foundChat = chat;
      foundUserId = userId;
    }
  });

  if (!foundChat) {
    return res.status(404).json({
      success: false,
      message: 'Chat not found'
    });
  }

  const adminMessage = {
    _id: 'msg_admin_' + Date.now(),
    sender: {
      id: 'admin-1',
      firstName: 'Support',
      lastName: 'Agent',
      role: 'admin'
    },
    content,
    type: 'text',
    createdAt: new Date().toISOString()
  };

  foundChat.messages.push(adminMessage);
  foundChat.lastActivity = new Date().toISOString();

  // Save data
  saveUserData();

  console.log('✅ Admin replied to chat:', req.params.chatId);

  res.status(201).json({
    success: true,
    message: 'Reply sent successfully',
    newMessage: adminMessage
  });
});

// Get admin notifications (new messages indicator)
app.get('/api/admin/message-notifications', (req, res) => {
  console.log('🔔 Admin message notifications request');

  res.json({
    success: true,
    notifications: adminNotifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)),
    unreadCount: adminNotifications.filter(n => !n.read).length
  });
});

// Mark admin notification as read
app.put('/api/admin/message-notifications/:id/read', (req, res) => {
  console.log('✅ Mark admin notification as read:', req.params.id);

  const notification = adminNotifications.find(n => n._id === req.params.id);

  if (!notification) {
    return res.status(404).json({
      success: false,
      message: 'Notification not found'
    });
  }

  notification.read = true;

  res.json({
    success: true,
    message: 'Notification marked as read'
  });
});

// Mark all admin notifications as read
app.put('/api/admin/message-notifications/mark-all-read', (req, res) => {
  console.log('✅ Mark all admin notifications as read');

  adminNotifications.forEach(notification => {
    notification.read = true;
  });

  res.json({
    success: true,
    message: 'All notifications marked as read'
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('❌ Server error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

const PORT = 3001;

// Create HTTP server
const server = createServer(app);

// Setup Socket.IO
const io = new Server(server, {
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:5173'],
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Socket.IO authentication middleware
io.use((socket, next) => {
  try {
    console.log('🔐 Socket.IO authentication attempt from:', socket.handshake.address);
    const token = socket.handshake.auth.token;

    if (!token) {
      console.log('❌ Socket connection rejected: No token provided');
      return next(new Error('Authentication error: No token provided'));
    }

    console.log('🔍 Verifying token:', token.substring(0, 20) + '...');

    // Verify JWT token
    const decoded = jwt.verify(token, 'your-secret-key');
    console.log('✅ Token decoded successfully for user ID:', decoded.id);

    const user = users.find(u => u.id === decoded.id);

    if (!user) {
      console.log('❌ Socket connection rejected: User not found for ID:', decoded.id);
      return next(new Error('Authentication error: User not found'));
    }

    // Attach user to socket
    socket.user = user;
    console.log('✅ Socket authenticated for user:', user.email, 'Role:', user.role);
    next();
  } catch (error) {
    console.log('❌ Socket authentication failed:', error.message);
    next(new Error('Authentication error: Invalid token'));
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('🔌 User connected:', socket.id, 'User:', socket.user.email);

  // Join user to their own room
  socket.join(socket.user.id);

  // Handle user joining specific rooms
  socket.on('join', (data) => {
    console.log('👤 User joined room:', data, 'User:', socket.user.email);
    socket.join(data.userId || data.roomId);
  });

  // Handle chat messages
  socket.on('chat_message', (data) => {
    console.log('💬 Chat message received:', data, 'From:', socket.user.email);
    // Broadcast to admin
    socket.broadcast.emit('new_message', {
      ...data,
      user: socket.user
    });
  });

  // Handle admin messages
  socket.on('admin_message', (data) => {
    console.log('👨‍💼 Admin message:', data, 'From:', socket.user.email);
    // Send to specific user
    socket.to(data.userId).emit('admin_reply', {
      ...data,
      admin: socket.user
    });
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log('🔌 User disconnected:', socket.id, 'User:', socket.user.email);
  });
});

server.listen(PORT, () => {
  console.log(`🚀 SecureBank Backend running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔑 Admin login: <EMAIL> / Admin123!`);
  console.log(`🔌 Socket.IO server running on port ${PORT}`);
  console.log(`✅ Server is ready to accept connections`);

  // Keep alive
  setInterval(() => {
    console.log('💓 Server heartbeat - still running...');
  }, 30000);
});

// Error handlers
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection:', reason);
});







//OnlineBank12!!
//