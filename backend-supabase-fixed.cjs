const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request timeout middleware
app.use((req, res, next) => {
  res.setTimeout(30000, () => {
    console.log('⏰ Request timeout for:', req.method, req.path);
    res.status(408).json({
      success: false,
      message: 'Request timeout'
    });
  });
  next();
});

// Request logging middleware
app.use((req, res, next) => {
  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Initialize Supabase client
console.log('🔗 Initializing Supabase connection...');
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Test Supabase connection
async function testSupabaseConnection() {
  try {
    console.log('🧪 Testing Supabase connection...');
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection error:', error.message);
    return false;
  }
}

// JWT helper functions
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

const getUserIdFromToken = (req) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  try {
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    return decoded.id;
  } catch (error) {
    console.error('❌ Token verification failed:', error.message);
    return null;
  }
};

// Optimized authentication middleware
const authenticateToken = async (req, res, next) => {
  try {
    const userId = getUserIdFromToken(req);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get user from Supabase with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .abortSignal(controller.signal)
      .single();

    clearTimeout(timeoutId);

    if (error || !user) {
      console.error('❌ User lookup failed:', error?.message || 'User not found');
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    req.userId = userId;
    req.user = user;
    next();
  } catch (error) {
    console.error('❌ Authentication middleware error:', error.message);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

// Admin middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  next();
};

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    success: true, 
    message: 'SecureBank API with Supabase (Fixed) is running',
    timestamp: new Date().toISOString()
  });
});

// Test endpoint
app.get('/test', async (req, res) => {
  try {
    console.log('🧪 Test endpoint called');
    const isConnected = await testSupabaseConnection();
    res.json({
      success: true,
      message: 'Test endpoint working',
      supabaseConnected: isConnected,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Test endpoint error:', error);
    res.status(500).json({
      success: false,
      message: 'Test failed',
      error: error.message
    });
  }
});

// Helper functions
const generateAccountNumber = () => {
  return Math.floor(********** + Math.random() * **********).toString();
};

const generateTransactionReference = () => {
  const prefix = 'TXN';
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
};

const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Initialize server
async function initializeServer() {
  try {
    console.log('🚀 Starting SecureBank API with Supabase (Fixed)...');
    
    // Test Supabase connection first
    const isConnected = await testSupabaseConnection();
    if (!isConnected) {
      console.error('❌ Cannot start server - Supabase connection failed');
      process.exit(1);
    }
    
    console.log('✅ Server initialization complete');
    return true;
  } catch (error) {
    console.error('❌ Server initialization failed:', error);
    process.exit(1);
  }
}

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('❌ Unhandled error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found'
  });
});

// Auth Routes
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('🔐 Login attempt for:', req.body.email);
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // Get user from database with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .abortSignal(controller.signal)
      .single();

    clearTimeout(timeoutId);

    if (error || !user) {
      console.log('❌ User not found:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      console.log('❌ Invalid password for:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Update last login (non-blocking)
    supabase
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id)
      .then(() => console.log('✅ Last login updated for:', email))
      .catch(err => console.error('⚠️ Failed to update last login:', err.message));

    // Generate token
    const token = generateToken(user.id);

    // Return user data (without password)
    const { password_hash, ...userWithoutPassword } = user;
    const userData = {
      ...userWithoutPassword,
      firstName: user.first_name,
      lastName: user.last_name,
      accountNumber: user.account_number,
      isActive: user.is_active,
      profileImage: user.profile_image,
      lastLogin: new Date().toISOString(),
      createdAt: user.created_at,
      updatedAt: user.updated_at
    };

    console.log('✅ Login successful for:', email);
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        user: userData
      }
    });

  } catch (error) {
    console.error('❌ Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

// Get current user
app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    const { password_hash, ...userWithoutPassword } = req.user;
    const userData = {
      ...userWithoutPassword,
      firstName: req.user.first_name,
      lastName: req.user.last_name,
      accountNumber: req.user.account_number,
      isActive: req.user.is_active,
      profileImage: req.user.profile_image,
      lastLogin: req.user.last_login,
      createdAt: req.user.created_at,
      updatedAt: req.user.updated_at
    };

    res.json({
      success: true,
      data: { user: userData }
    });
  } catch (error) {
    console.error('❌ Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user data'
    });
  }
});

// User Routes
app.get('/api/user/profile', authenticateToken, async (req, res) => {
  try {
    const { password_hash, ...userWithoutPassword } = req.user;
    const userData = {
      ...userWithoutPassword,
      firstName: req.user.first_name,
      lastName: req.user.last_name,
      accountNumber: req.user.account_number,
      isActive: req.user.is_active,
      profileImage: req.user.profile_image,
      lastLogin: req.user.last_login,
      createdAt: req.user.created_at,
      updatedAt: req.user.updated_at
    };

    res.json({
      success: true,
      data: { user: userData }
    });
  } catch (error) {
    console.error('❌ Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile'
    });
  }
});

app.get('/api/user/balance', authenticateToken, async (req, res) => {
  try {
    res.json({
      success: true,
      data: { balance: req.user.balance }
    });
  } catch (error) {
    console.error('❌ Get balance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get balance'
    });
  }
});

app.get('/api/user/transactions', authenticateToken, async (req, res) => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const { data: transactions, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: false })
      .abortSignal(controller.signal);

    clearTimeout(timeoutId);

    if (error) {
      throw error;
    }

    // Transform data to match frontend expectations
    const transformedTransactions = transactions.map(t => ({
      id: t.id,
      type: t.type,
      amount: t.amount,
      reference: t.reference,
      description: t.description,
      recipientName: t.recipient_name,
      recipientAccount: t.recipient_account,
      purpose: t.purpose,
      status: t.status,
      sentFrom: t.sent_from,
      createdAt: t.created_at,
      updatedAt: t.updated_at
    }));

    res.json({
      success: true,
      data: { transactions: transformedTransactions }
    });
  } catch (error) {
    console.error('❌ Get transactions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transactions'
    });
  }
});

app.get('/api/user/notifications', authenticateToken, async (req, res) => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: false })
      .abortSignal(controller.signal);

    clearTimeout(timeoutId);

    if (error) {
      throw error;
    }

    // Transform data to match frontend expectations
    const transformedNotifications = notifications.map(n => ({
      _id: n.id,
      type: n.type,
      title: n.title,
      message: n.message,
      read: n.read,
      createdAt: n.created_at,
      updatedAt: n.updated_at
    }));

    res.json({
      success: true,
      data: { notifications: transformedNotifications }
    });
  } catch (error) {
    console.error('❌ Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notifications'
    });
  }
});

// Chat Routes
app.get('/api/chat', authenticateToken, async (req, res) => {
  try {
    console.log('💬 Getting chats for user:', req.userId);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: true })
      .abortSignal(controller.signal);

    clearTimeout(timeoutId);

    if (error) {
      throw error;
    }

    if (messages.length === 0) {
      return res.json({
        success: true,
        data: { chats: [] }
      });
    }

    // Group messages into a single chat
    const transformedMessages = messages.map(m => ({
      _id: m.id,
      content: m.content,
      sender: { role: m.sender_type },
      timestamp: m.created_at
    }));

    const chat = {
      _id: `chat_${req.userId}`,
      subject: 'Support Chat',
      user: {
        id: req.user.id,
        firstName: req.user.first_name,
        lastName: req.user.last_name,
        email: req.user.email,
        accountNumber: req.user.account_number
      },
      messages: transformedMessages,
      status: 'open',
      priority: 'medium',
      createdAt: messages[0].created_at,
      lastActivity: messages[messages.length - 1].created_at
    };

    console.log(`✅ Found ${messages.length} chat messages for user`);
    res.json({
      success: true,
      data: { chats: [chat] }
    });

  } catch (error) {
    console.error('❌ Get chats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get chats'
    });
  }
});

app.post('/api/chat/:id/messages', authenticateToken, async (req, res) => {
  try {
    console.log('💬 Sending message to chat:', req.params.id);
    const { content } = req.body;

    if (!content || !content.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Message content is required'
      });
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    // Insert message
    const { data: newMessage, error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: req.userId,
        sender_type: 'user',
        content: content.trim(),
        status: 'open'
      })
      .select()
      .abortSignal(controller.signal)
      .single();

    clearTimeout(timeoutId);

    if (error) {
      throw error;
    }

    // Transform data to match frontend expectations
    const transformedMessage = {
      _id: newMessage.id,
      content: newMessage.content,
      sender: { role: 'user' },
      timestamp: newMessage.created_at,
      status: newMessage.status
    };

    console.log('✅ Message sent successfully');
    res.json({
      success: true,
      message: 'Message sent successfully',
      data: { message: transformedMessage }
    });

  } catch (error) {
    console.error('❌ Send chat message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message'
    });
  }
});

// Start server
async function startServer() {
  try {
    await initializeServer();

    const PORT = process.env.PORT || 3002;
    const server = app.listen(PORT, () => {
      console.log(`🚀 SecureBank API with Supabase (Fixed) running on port ${PORT}`);
      console.log(`🔗 Health check: http://localhost:${PORT}/health`);
      console.log(`🧪 Test endpoint: http://localhost:${PORT}/test`);
    });

    server.on('error', (error) => {
      console.error('❌ Server error:', error);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { app, supabase, authenticateToken, requireAdmin, generateToken, generateAccountNumber, generateTransactionReference, generateOTP };
