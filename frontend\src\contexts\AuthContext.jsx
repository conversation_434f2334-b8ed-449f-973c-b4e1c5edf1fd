import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI } from '../services/api';
import socketService from '../services/socket';
import toast from 'react-hot-toast';

const AuthContext = createContext();

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload, isAuthenticated: !!action.payload };
    case 'SET_TOKEN':
      return { ...state, token: action.payload };
    case 'LOGOUT':
      return { ...state, user: null, token: null, isAuthenticated: false };
    case 'UPDATE_USER':
      return { ...state, user: { ...state.user, ...action.payload } };
    default:
      return state;
  }
};

const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  loading: true,
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('token');
      const userData = localStorage.getItem('user');

      if (token && userData) {
        try {
          // Parse stored user data first
          const storedUser = JSON.parse(userData);

          // Verify token is still valid and get fresh data
          const response = await authAPI.getMe();
          const freshUser = response.data.data.user;

          // Merge stored data with fresh data, prioritizing fresh data but keeping stored profileImage if fresh is missing
          const mergedUser = {
            ...storedUser,
            ...freshUser,
            // If fresh data doesn't have profileImage but stored data does, keep stored
            profileImage: freshUser.profileImage || storedUser.profileImage || null
          };

          dispatch({ type: 'SET_USER', payload: mergedUser });
          dispatch({ type: 'SET_TOKEN', payload: token });

          // Update localStorage with merged data
          localStorage.setItem('user', JSON.stringify(mergedUser));

          // Socket.IO connection is now optional and only enabled for specific features
          // console.log('✅ Token validated successfully - Socket.IO available for real-time features');

        } catch (error) {
          console.error('Token validation failed:', error);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          dispatch({ type: 'LOGOUT' });
        }
      }

      dispatch({ type: 'SET_LOADING', payload: false });
    };

    initAuth();
  }, []);

  const login = async (credentials) => {
    try {
      // console.log('Login attempt with credentials:', credentials);
      dispatch({ type: 'SET_LOADING', payload: true });

      const response = await authAPI.login(credentials);
      // console.log('Login response:', response);
      const { token, user } = response.data;

      // Store in localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      // Update state
      dispatch({ type: 'SET_TOKEN', payload: token });
      dispatch({ type: 'SET_USER', payload: user });

      // Socket.IO connection is now optional and only enabled for specific features
      // console.log('✅ Login successful - Socket.IO available for real-time features');

      toast.success('Login successful!');
      return { success: true, user };
    } catch (error) {
      console.error('Login error:', error);
      const message = error.response?.data?.message || 'Login failed';
      toast.error(message);
      return { success: false, message };
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const register = async (userData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const response = await authAPI.register(userData);
      const { token, user } = response.data;

      // Store in localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      // Update state
      dispatch({ type: 'SET_TOKEN', payload: token });
      dispatch({ type: 'SET_USER', payload: user });

      // Socket.IO connection is now optional and only enabled for specific features
      // console.log('✅ Registration successful - Socket.IO available for real-time features');

      toast.success('Registration successful!');
      return { success: true, user };
    } catch (error) {
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
      return { success: false, message };
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Disconnect socket
      socketService.disconnect();

      // Update state
      dispatch({ type: 'LOGOUT' });

      toast.success('Logged out successfully');
    }
  };

  const updateUser = (userData, options = {}) => {
    dispatch({ type: 'UPDATE_USER', payload: userData });

    // Only update localStorage if this is a permanent update (not temporary like test images)
    if (!options.temporary) {
      const updatedUser = { ...state.user, ...userData };

      // Ensure profileImage is explicitly handled
      if (userData.hasOwnProperty('profileImage')) {
        updatedUser.profileImage = userData.profileImage;
      }

      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
