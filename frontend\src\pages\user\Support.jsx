import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { chatAPI } from '../../services/api';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';

const Support = () => {
  const { user } = useAuth();
  const [chats, setChats] = useState([]);
  const [activeChat, setActiveChat] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [showNewChatForm, setShowNewChatForm] = useState(false);
  const [endingChat, setEndingChat] = useState(false);
  const [newChatData, setNewChatData] = useState({
    subject: '',
    message: '',
    priority: 'medium'
  });
  const messagesEndRef = useRef(null);

  useEffect(() => {
    fetchChats();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [activeChat?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchChats = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getChats();
      setChats(response.data.data.chats);
      
      // If no active chat and chats exist, select the first one
      if (!activeChat && response.data.chats.length > 0) {
        setActiveChat(response.data.chats[0]);
      }
    } catch (error) {
      console.error('Error fetching chats:', error);
      toast.error('Failed to load chats');
    } finally {
      setLoading(false);
    }
  };

  const createNewChat = async (e) => {
    e.preventDefault();

    if (!newChatData.subject.trim() || !newChatData.message.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const response = await chatAPI.createChat(newChatData);
      const newChat = response.data.chat;

      setChats(prev => [newChat, ...prev]);
      setActiveChat(newChat);
      setShowNewChatForm(false);
      setNewChatData({ subject: '', message: '', priority: 'medium' });

      toast.success('Chat created successfully!');

      // Poll for agent response after 3 seconds
      setTimeout(() => {
        fetchChats();
      }, 3000);
    } catch (error) {
      console.error('Error creating chat:', error);
      toast.error('Failed to create chat');
    }
  };

  const endChat = async () => {
    if (!activeChat) return;

    if (!window.confirm('Are you sure you want to end this chat? This will permanently delete the chat history.')) {
      return;
    }

    try {
      setEndingChat(true);

      // Call delete endpoint using chatAPI
      await chatAPI.endChat(activeChat._id);

      // Remove chat from local state
      setChats(prev => prev.filter(chat => chat._id !== activeChat._id));
      setActiveChat(null);

      toast.success('Chat ended successfully');
    } catch (error) {
      console.error('Error ending chat:', error);
      toast.error('Failed to end chat');
    } finally {
      setEndingChat(false);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !activeChat) return;

    try {
      setSendingMessage(true);
      
      const response = await chatAPI.sendMessage(activeChat._id, newMessage);
      const sentMessage = response.data.newMessage;
      
      // Update the active chat with the new message
      setActiveChat(prev => ({
        ...prev,
        messages: [...prev.messages, sentMessage]
      }));
      
      // Update the chat in the list
      setChats(prev => prev.map(chat => 
        chat._id === activeChat._id 
          ? { ...chat, messages: [...chat.messages, sentMessage] }
          : chat
      ));
      
      setNewMessage('');
      toast.success('Message sent!');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="mt-4 text-gray-600">Loading support...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-4 px-4 sm:py-6 sm:px-6 lg:px-8">
        <div className="py-4 sm:py-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Support Center</h1>
              <p className="mt-1 sm:mt-2 text-sm sm:text-base text-gray-600">
                Get help from our support team
              </p>
            </div>
            <Button onClick={() => setShowNewChatForm(true)} size="sm" className="w-full sm:w-auto">
              New Chat
            </Button>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
            {/* Chat List */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader className="px-4 sm:px-6 py-3 sm:py-4">
                  <CardTitle className="text-base sm:text-lg">Your Chats</CardTitle>
                </CardHeader>
                <CardContent className="px-4 sm:px-6 py-3 sm:py-4">
                  {chats.length === 0 ? (
                    <div className="text-center py-6 sm:py-8">
                      <div className="text-gray-400 text-3xl sm:text-4xl mb-3 sm:mb-4">💬</div>
                      <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No chats yet</h3>
                      <p className="text-sm sm:text-base text-gray-500 mb-3 sm:mb-4">Start a conversation with our support team</p>
                      <Button onClick={() => setShowNewChatForm(true)} size="sm" className="w-full sm:w-auto">
                        Start Chat
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                      {chats.map((chat) => (
                        <div
                          key={chat._id}
                          onClick={() => setActiveChat(chat)}
                          className={`p-3 rounded-lg cursor-pointer transition-all duration-200 border ${
                            activeChat?._id === chat._id
                              ? 'bg-primary-50 border-primary-200 shadow-sm'
                              : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm'
                          }`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium text-gray-900 truncate text-sm pr-2">
                              {chat.subject}
                            </h4>
                            <div className="flex flex-col space-y-1 flex-shrink-0">
                              <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(chat.priority)}`}>
                                {chat.priority}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(chat.status)}`}>
                                {chat.status}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between mb-1">
                            <p className="text-xs text-gray-600 font-medium">
                              💬 Latest Message
                            </p>
                            <p className="text-xs text-gray-400">
                              {formatDate(chat.lastActivity)}
                            </p>
                          </div>
                          <p className="text-xs text-gray-500 truncate">
                            {chat.messages[chat.messages.length - 1]?.content || 'No messages'}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                              {chat.messages.length} msg{chat.messages.length !== 1 ? 's' : ''}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded ${
                              chat.status === 'open' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'
                            }`}>
                              {chat.status === 'open' ? '🟢 Active' : '⚪ Closed'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Chat Messages */}
            <div className="lg:col-span-2">
              {activeChat ? (
                <Card className="h-[600px] flex flex-col">
                  <CardHeader className="flex-shrink-0 border-b border-gray-200 p-3 sm:p-4">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
                      <div className="min-w-0 flex-1">
                        <CardTitle className="text-base sm:text-lg truncate">{activeChat.subject}</CardTitle>
                        <div className="flex flex-wrap gap-1 sm:gap-2 mt-1 sm:mt-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(activeChat.priority)}`}>
                            {activeChat.priority} priority
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(activeChat.status)}`}>
                            {activeChat.status}
                          </span>
                          <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                            {activeChat.messages.length} msg{activeChat.messages.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={endChat}
                        loading={endingChat}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 w-full sm:w-auto text-xs sm:text-sm px-3 py-2"
                      >
                        {endingChat ? 'Ending...' : 'End Chat'}
                      </Button>
                    </div>
                  </CardHeader>

                  {/* Fixed Height Messages Container */}
                  <div className="flex-1 flex flex-col min-h-0">
                    <div className="flex-1 overflow-y-auto px-4 py-4 bg-gray-50 chat-messages" style={{ maxHeight: '400px' }}>
                      {activeChat.messages.length === 0 ? (
                        <div className="flex items-center justify-center h-full min-h-[200px]">
                          <div className="text-center">
                            <div className="text-gray-400 text-4xl mb-2">💬</div>
                            <p className="text-gray-500">No messages in this conversation yet</p>
                            <p className="text-gray-400 text-sm mt-1">Start by sending a message below</p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {activeChat.messages.map((message) => (
                            <div
                              key={message._id}
                              className={`flex ${
                                message.sender.role === 'user' ? 'justify-end' : 'justify-start'
                              }`}
                            >
                              <div
                                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg shadow-sm message-bubble ${
                                  message.sender.role === 'user'
                                    ? 'bg-primary-600 text-white'
                                    : 'bg-white text-gray-900 border border-gray-200'
                                }`}
                              >
                                <div className="flex items-center space-x-2 mb-1">
                                  <p className={`text-xs font-medium ${
                                    message.sender.role === 'user' ? 'text-primary-100' : 'text-gray-600'
                                  }`}>
                                    {message.sender.role === 'user' ? '👤 You' : '🛡️ Support Agent'}
                                  </p>
                                </div>
                                <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
                                <p className={`text-xs mt-2 ${
                                  message.sender.role === 'user' ? 'text-primary-100' : 'text-gray-500'
                                }`}>
                                  {formatDate(message.createdAt)}
                                </p>
                              </div>
                            </div>
                          ))}
                          <div ref={messagesEndRef} />
                        </div>
                      )}
                    </div>

                    {/* Message Input */}
                    <div className="border-t border-gray-200 bg-white px-3 sm:px-4 py-3 sm:py-4 flex-shrink-0">
                      <form onSubmit={sendMessage} className="flex space-x-2 sm:space-x-3">
                        <div className="flex-1">
                          <Input
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            placeholder="Type your message..."
                            className="w-full text-sm"
                            disabled={sendingMessage}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                if (newMessage.trim()) {
                                  sendMessage(e);
                                }
                              }
                            }}
                          />
                        </div>
                        <Button
                          type="submit"
                          loading={sendingMessage}
                          disabled={!newMessage.trim()}
                          className="px-3 sm:px-6 text-xs sm:text-sm"
                        >
                          {sendingMessage ? 'Sending...' : 'Send'}
                        </Button>
                      </form>
                      <p className="text-xs text-gray-500 mt-1 sm:mt-2">
                        Press Enter to send, Shift+Enter for new line
                      </p>
                    </div>
                  </div>
                </Card>
              ) : (
                <Card className="h-[600px] flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-gray-400 text-6xl mb-4">💬</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Select a chat</h3>
                    <p className="text-gray-500">Choose a chat from the list to start messaging</p>
                  </div>
                </Card>
              )}
            </div>
          </div>
        </div>

        {/* New Chat Modal */}
        {showNewChatForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Start New Chat</h3>
              <form onSubmit={createNewChat} className="space-y-4">
                <Input
                  label="Subject"
                  value={newChatData.subject}
                  onChange={(e) => setNewChatData(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="What do you need help with?"
                  required
                />
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <select
                    value={newChatData.priority}
                    onChange={(e) => setNewChatData(prev => ({ ...prev, priority: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Message
                  </label>
                  <textarea
                    value={newChatData.message}
                    onChange={(e) => setNewChatData(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Describe your issue..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowNewChatForm(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button type="submit" className="flex-1">
                    Start Chat
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Support;
